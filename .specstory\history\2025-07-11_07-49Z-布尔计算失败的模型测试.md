<!-- Generated by SpecStory -->

# 布尔计算失败的模型测试 (2025-07-11 07:49Z)

_**User**_

D:\Users\Administrator\Desktop\190-shiniugou-des\ts\05\123\1_05-J2a_1.ts ,  D:\Users\Administrator\Desktop\190-shiniugou-des\ts\05\123\extruded_surface.ts         图中2个模型是有交集的，为什么布尔计算会失败。仔细研究下，写一个新的py文件测试。目前的布尔计算是将ts转成obj，我希望使用pyvista转成vtk，然后使用pymeshlab进行布尔计算。写个测试代码，不要啰嗦，只写一个

---

_**Assistant**_

<think><details><summary>Thought Process</summary>用户遇到了布尔运算失败的问题，错误信息是"Vertex matrix is empty"。用户提到两个模型是有交集的，但布尔计算失败了。

目前的流程是：
1. 读取TS文件 
2. 转换为OBJ格式
3. 使用pymeshlab进行布尔运算

用户希望改用：
1. 读取TS文件
2. 使用pyvista转换为VTK格式
3. 使用pymeshlab进行布尔运算

问题可能出现在：
1. OBJ格式转换过程中丢失了数据
2. 网格质量问题
3. 坐标系或尺度问题

让我写一个测试代码，使用pyvista转换为VTK，然后用pymeshlab进行布尔运算。

用户要求：
- 新的py文件测试
- 使用pyvista转成vtk
- 使用pymeshlab进行布尔计算
- 不要啰嗦，只写一个测试代码

我需要：
1. 读取TS文件的函数（可以复用现有的read_tsurf_data）
2. 使用pyvista创建mesh并保存为VTK
3. 使用pymeshlab加载VTK并进行布尔运算</details></think>

---

我来写一个测试代码，使用pyvista转换为VTK格式进行布尔运算：

---

```diff
- 
+ import numpy as np
+ import pyvista as pv
+ import pymeshlab
+ import re
+ import os
+ 
+ def read_tsurf_data(file_path):
+     """读取TS文件数据"""
+     encodings = ['utf-8', 'gbk', 'iso-8859-1']
+     
+     for encoding in encodings:
+         try:
+             vrtx = []
+             vrtx_map = {}
+             trgl = []
+             current_idx = 0
+             
+             with open(file_path, 'r', encoding=encoding) as file:
+                 for line_num, line in enumerate(file, 1):
+                     if 'VRTX' in line:
+                         l_input = re.split(r'[\s*]', line)
+                         try:
+                             nums = [x for x in l_input[2:] if x and x.replace('.', '').replace('-', '').isdigit()]
+                             if len(nums) >= 3:
+                                 vertex_id = int(l_input[1])
+                                 coords = [float(nums[0]), float(nums[1]), float(nums[2])]
+                                 vrtx.append(coords)
+                                 vrtx_map[vertex_id] = current_idx
+                                 current_idx += 1
+                         except (ValueError, IndexError):
+                             continue
+ 
+                     elif 'ATOM' in line:
+                         l_input = re.split(r'[\s*]', line)
+                         if len(l_input) > 2:
+                             try:
+                                 vertex_id_atom = int(l_input[2])
+                                 if vertex_id_atom in vrtx_map:
+                                     orig_idx = vrtx_map[vertex_id_atom]
+                                     vrtx.append(vrtx[orig_idx])
+                                     new_id = int(l_input[1])
+                                     vrtx_map[new_id] = current_idx
+                                     current_idx += 1
+                             except (ValueError, IndexError):
+                                 continue
+ 
+                     elif 'TRGL' in line:
+                         l_input = re.split(r'[\s*]', line)
+                         try:
+                             nums = [x for x in l_input[1:] if x and x.isdigit()]
+                             if len(nums) == 3:
+                                 v1 = vrtx_map[int(nums[0])]
+                                 v2 = vrtx_map[int(nums[1])]
+                                 v3 = vrtx_map[int(nums[2])]
+                                 trgl.append([v1, v2, v3])
+                         except (ValueError, IndexError, KeyError):
+                             continue
+ 
+             if len(vrtx) > 0 and len(trgl) > 0:
+                 print(f"成功读取 {file_path}，使用编码 {encoding}")
+                 print(f"读取了 {len(vrtx)} 个顶点和 {len(trgl)} 个三角面")
+                 return np.array(vrtx), np.array(trgl)
+                 
+         except UnicodeDecodeError:
+             continue
+         except Exception as e:
+             print(f"读取 {file_path} 时发生错误: {e}")
+             continue
+     
+     print(f"无法读取 {file_path}")
+     return np.array([]), np.array([])
+ 
+ def ts_to_vtk_pyvista(ts_file, vtk_file):
+     """使用pyvista将TS文件转换为VTK格式"""
+     vertices, faces = read_tsurf_data(ts_file)
+     if len(vertices) == 0 or len(faces) == 0:
+         print(f"无法从TS文件读取数据: {ts_file}")
+         return False
+     
+     # 创建faces数组，pyvista格式需要在每个面前加上点数
+     faces_with_header = []
+     for face in faces:
+         faces_with_header.extend([3, face[0], face[1], face[2]])
+     faces_array = np.array(faces_with_header)
+     
+     # 创建pyvista网格
+     mesh = pv.PolyData(vertices, faces_array)
+     
+     # 清理网格
+     mesh = mesh.clean()
+     mesh = mesh.triangulate()
+     
+     # 保存为VTK
+     mesh.save(vtk_file)
+     print(f"已转换并保存VTK文件: {vtk_file}")
+     print(f"网格信息: {mesh.n_points}点, {mesh.n_cells}面")
+     return True
+ 
+ def test_boolean_operations():
+     """测试布尔运算"""
+     # 输入文件路径
+     ts_file1 = r"D:\Users\Administrator\Desktop\190-shiniugou-des\ts\05\123\1_05-J2a_1.ts"
+     ts_file2 = r"D:\Users\Administrator\Desktop\190-shiniugou-des\ts\05\123\extruded_surface.ts"
+     
+     # 输出目录
+     output_dir = r"D:\Users\Administrator\Desktop\190-shiniugou-des\ts\05\123"
+     
+     # 转换为VTK
+     vtk_file1 = os.path.join(output_dir, "model1.vtk")
+     vtk_file2 = os.path.join(output_dir, "model2.vtk")
+     
+     print("=== 转换TS文件为VTK格式 ===")
+     if not ts_to_vtk_pyvista(ts_file1, vtk_file1):
+         return
+     if not ts_to_vtk_pyvista(ts_file2, vtk_file2):
+         return
+     
+     print("\n=== 使用PyMeshLab进行布尔运算 ===")
+     try:
+         # 创建MeshSet并加载VTK文件
+         ms = pymeshlab.MeshSet()
+         ms.load_new_mesh(vtk_file1)
+         ms.load_new_mesh(vtk_file2)
+         
+         # 检查加载的网格
+         mesh1 = ms.mesh(0)
+         mesh2 = ms.mesh(1)
+         print(f"模型1: {mesh1.vertex_number()}顶点, {mesh1.face_number()}面")
+         print(f"模型2: {mesh2.vertex_number()}顶点, {mesh2.face_number()}面")
+         
+         # 基本网格修复
+         print("进行基本网格修复...")
+         for i in range(2):
+             ms.set_current_mesh(i)
+             ms.meshing_remove_duplicate_vertices()
+             ms.meshing_remove_duplicate_faces()
+             ms.meshing_repair_non_manifold_edges()
+             ms.meshing_repair_non_manifold_vertices()
+             ms.compute_normal_per_face()
+             ms.compute_normal_per_vertex()
+             print(f"修复后模型{i+1}: {ms.current_mesh().vertex_number()}顶点, {ms.current_mesh().face_number()}面")
+         
+         # 执行交集运算
+         print("执行交集运算...")
+         ms.set_current_mesh(0)
+         ms.generate_boolean_intersection(first_mesh=0, second_mesh=1)
+         
+         # 删除原始网格，保留结果
+         ms.set_current_mesh(2)  # 结果网格
+         result_mesh = ms.current_mesh()
+         print(f"交集结果: {result_mesh.vertex_number()}顶点, {result_mesh.face_number()}面")
+         
+         # 保存结果
+         intersection_vtk = os.path.join(output_dir, "intersection_result.vtk")
+         intersection_obj = os.path.join(output_dir, "intersection_result.obj")
+         
+         ms.save_current_mesh(intersection_vtk)
+         ms.save_current_mesh(intersection_obj)
+         print(f"交集结果已保存: {intersection_vtk}")
+         print(f"交集结果已保存: {intersection_obj}")
+         
+         # 执行差集运算
+         print("执行差集运算...")
+         ms.set_current_mesh(0)
+         ms.generate_boolean_difference(first_mesh=0, second_mesh=1)
+         
+         diff_mesh = ms.current_mesh()
+         print(f"差集结果: {diff_mesh.vertex_number()}顶点, {diff_mesh.face_number()}面")
+         
+         # 保存差集结果
+         difference_vtk = os.path.join(output_dir, "difference_result.vtk")
+         difference_obj = os.path.join(output_dir, "difference_result.obj")
+         
+         ms.save_current_mesh(difference_vtk)
+         ms.save_current_mesh(difference_obj)
+         print(f"差集结果已保存: {difference_vtk}")
+         print(f"差集结果已保存: {difference_obj}")
+         
+         # 执行并集运算
+         print("执行并集运算...")
+         ms.set_current_mesh(0)
+         ms.generate_boolean_union(first_mesh=0, second_mesh=1)
+         
+         union_mesh = ms.current_mesh()
+         print(f"并集结果: {union_mesh.vertex_number()}顶点, {union_mesh.face_number()}面")
+         
+         # 保存并集结果
+         union_vtk = os.path.join(output_dir, "union_result.vtk")
+         union_obj = os.path.join(output_dir, "union_result.obj")
+         
+         ms.save_current_mesh(union_vtk)
+         ms.save_current_mesh(union_obj)
+         print(f"并集结果已保存: {union_vtk}")
+         print(f"并集结果已保存: {union_obj}")
+         
+         print("布尔运算测试完成！")
+         
+     except Exception as e:
+         print(f"布尔运算失败: {e}")
+         import traceback
+         traceback.print_exc()
+ 
+ if __name__ == "__main__":
+     test_boolean_operations() 
```

---

<think><details><summary>Thought Process</summary>我已经创建了一个测试文件，使用pyvista将TS文件转换为VTK格式，然后用pymeshlab进行布尔运算。这个测试代码：

1. 使用pyvista读取TS文件并转换为VTK格式
2. 对网格进行清理和三角化
3. 使用pymeshlab加载VTK文件
4. 进行基本的网格修复
5. 执行交集、差集、并集运算
6. 保存结果

这应该能解决之前"Vertex matrix is empty"的问题，因为：
1. pyvista的网格处理更加稳定
2. VTK格式保持了更好的网格完整性
3. 在pymeshlab中增加了网格修复步骤

用户可以直接运行这个测试文件来验证布尔运算是否能正常工作。</details></think>

---

已创建测试文件 `test_boolean_vtk.py`。这个测试代码使用pyvista将TS文件转换为VTK格式，然后用pymeshlab进行布尔运算。

主要改进：
1. **使用pyvista转换**：更稳定的网格处理和VTK格式输出
2. **网格修复**：在布尔运算前修复网格缺陷
3. **完整测试**：包含交集、差集、并集三种运算

直接运行测试：
```bash
python test_boolean_vtk.py
```

这应该能解决"Vertex matrix is empty"的问题，因为VTK格式比OBJ更好地保持了网格的完整性。

---

_**User**_

vtk 不行的换导出成wrl格式

---

_**Assistant**_

<think><details><summary>Thought Process</summary>用户说VTK格式不行，pymeshlab不支持加载VTK格式。用户建议换成WRL格式。我需要修改测试代码，将VTK格式改为WRL格式。

WRL是VRML格式，pyvista应该支持导出。让我修改代码。</details></think>

---



---



---

