<!-- Generated by SpecStory -->

# Integrating GUI and Adjusting Window Size (2025-05-15 15:51:58)

_**User**_

@geospatial_analyzer_gui_qt.py 将其集成到mspview06.py中，并修改mspview06.py的默认窗口大小，我希望能显示mspview06.py全部模块

---

_**Assistant**_



---

好的，我将把 `geospatial_analyzer_gui_qt.py` 的功能集成到 `mspview06.py` 中，并调整 `mspview06.py` 的默认窗口大小，以便更好地显示所有模块。

集成方式如下：
1.  我会在 `mspview06.py` 中添加一个新的顶级菜单，例如 "🌟品位分析工具"。
2.  点击该菜单下的选项时，会实例化并显示 `GeospatialAnalyzerQtApp` 窗口，使其作为一个独立的工具运行。
3.  `mspview06.py` 的主窗口大小将被调整，以容纳新增的菜单项并提供更宽敞的界面。

现在，我将对 `mspview06.py` 文件进行修改。
我将首先进行导入，然后调整窗口大小，最后添加新的菜单项及其对应的功能。



---

```diff
  # 导入钻孔转mark工具
  import excel_to_mark
+ # 导入品位分析工具
+ from geospatial_analyzer_gui_qt import GeospatialAnalyzerQtApp
  
  warnings.filterwarnings("ignore", category=UserWarning, message="Unknown property.*")
```

```diff
          developer_label.setStyleSheet("color: #0056b3; font-weight: bold;")
          self.statusBar.addPermanentWidget(developer_label)
-         self.setGeometry(300, 300, 1100, 700)
+         # 调整窗口大小和位置
+         self.setGeometry(150, 150, 1350, 750)
          
          # 创建中央部件
```

```diff
          self.initExcelToMarkUI()
          self.initMenus_08()
+         
+         # 初始化品位分析工具菜单
+         self.initGeospatialAnalyzerMenu()
          
          # 显示界面
```

```diff
          menu.addAction(action)
  
+     def initGeospatialAnalyzerMenu(self):
+         """初始化矿体品位分析与可视化工具菜单"""
+         menu = self.menuBar().addMenu('🌟品位分析工具')
+         
+         action = QAction('打开品位分析工具', self)
+         action.setIcon(self.style().standardIcon(self.style().SP_ComputerIcon)) # 使用一个合适的图标
+         action.triggered.connect(self.openGeospatialAnalyzerTool)
+         menu.addAction(action)
+ 
      def openMiningVoidAreaTool(self):
          """打开采空区处理集成工具"""
```

```diff
              QMessageBox.critical(self, "错误", error_msg)
  
+     def openGeospatialAnalyzerTool(self):
+         """打开矿体品位分析与可视化工具"""
+         try:
+             self.add_log_message("正在打开矿体品位分析与可视化工具...")
+             # 将 geospatial_analyzer_tool 存储为实例变量，以便管理其生命周期 (如果需要)
+             # 或者就让它成为一个局部变量，关闭时由Python GC处理
+             self.geospatial_analyzer_tool_instance = GeospatialAnalyzerQtApp()
+             self.geospatial_analyzer_tool_instance.show()
+             self.add_log_message("矿体品位分析与可视化工具已成功打开。")
+             self.statusBar.showMessage("已打开矿体品位分析与可视化工具")
+         except Exception as e:
+             error_msg = f"打开矿体品位分析与可视化工具失败: {str(e)}"
+             self.add_log_message(f"错误: {error_msg}")
+             QMessageBox.critical(self, "错误", error_msg)
+ 
      def cleanup(self):
          # 清理任何打开的文件
```

---

`mspview06.py` 已成功修改。

**总结更改**：
1.  在 `mspview06.py` 顶部添加了 `from geospatial_analyzer_gui_qt import GeospatialAnalyzerQtApp`。
2.  在 `TSurfGenerator.initUI` 方法中，将 `self.setGeometry(300, 300, 1100, 700)` 修改为 `self.setGeometry(150, 150, 1350, 750)`，以增大窗口并调整其初始位置。
3.  添加了新的方法 `initGeospatialAnalyzerMenu` 来创建 "🌟品位分析工具" 菜单。
4.  添加了 `openGeospatialAnalyzerTool` 方法，用于在点击菜单项时创建并显示 `GeospatialAnalyzerQtApp` 的实例。
5.  在 `initUI` 方法的末尾（在其他菜单初始化之后，`self.show()` 之前）调用了 `self.initGeospatialAnalyzerMenu()`。

现在，当您运行 `mspview06.py` 时，应该会看到一个更大的主窗口，并且菜单栏中会有一个新的 "🌟品位分析工具" 选项。点击该选项下的 "打开品位分析工具" 将会启动 `GeospatialAnalyzerQtApp` 界面。

请确保 `geospatial_analyzer_gui_qt.py` 文件与 `mspview06.py` 位于同一目录下，或者在Python的搜索路径中，以便导入能够成功。根据您的项目结构，它们似乎在同一个工作区根目录，这应该没问题。

---

_**User**_

品位分析工具 退出后出错

---

_**Assistant**_



---

您好，您遇到的 `wglMakeCurrent failed` 错误通常与 VTK 和 Qt 窗口关闭时 OpenGL 上下文未能正确释放有关。当 “品位分析工具” (`GeospatialAnalyzerQtApp`) 关闭后，它内部的 PyVista QtInteractor (`pv_widget`) 可能没有被正确地清理。

为了解决这个问题，我将在 `geospatial_analyzer_gui_qt.py` 文件中的 `GeospatialAnalyzerQtApp` 类里添加一个 `closeEvent` 方法。这个方法会在窗口关闭时被调用，我们可以在其中确保 PyVista 的渲染器得到妥善关闭。

我将对 `geospatial_analyzer_gui_qt.py` 文件进行以下修改：
在 `GeospatialAnalyzerQtApp` 类中，添加 `closeEvent` 方法来调用 `self.pv_widget.close()`。



---

```diff
          QMessageBox.information(self, "导出成功", f"已导出{len(self.current_exportable_ore_voxels)}个矿体的体素数据。")
  
+     def closeEvent(self, event):
+         """处理窗口关闭事件，确保PyVista部件被正确关闭"""
+         self.log_message("关闭品位分析工具窗口...", prefix="INFO: ")
+         if hasattr(self, 'pv_widget') and self.pv_widget is not None:
+             self.pv_widget.close()
+         super().closeEvent(event)
+ 
  # 程序入口
  if __name__ == "__main__":
```

---

我已经向 `geospatial_analyzer_gui_qt.py` 文件中的 `GeospatialAnalyzerQtApp` 类添加了 `closeEvent` 方法。

这个修改应该能确保在关闭“品位分析工具”窗口时，其内部的 PyVista 组件（`pv_widget`）能够正确释放 OpenGL 资源，从而避免您看到的 `wglMakeCurrent failed` 错误。

请您再次尝试运行程序并关闭“品位分析工具”，看看问题是否已解决。

---

