#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Test script to check import compatibility
"""

print("Testing imports...")

try:
    import numpy as np
    print(f"✓ NumPy imported successfully. Version: {np.__version__}")
except Exception as e:
    print(f"✗ NumPy import failed: {e}")

try:
    import pandas as pd
    print(f"✓ Pandas imported successfully. Version: {pd.__version__}")
except Exception as e:
    print(f"✗ Pandas import failed: {e}")

try:
    from matplotlib.path import Path as mplPath
    print("✓ matplotlib.path imported successfully")
except Exception as e:
    print(f"✗ matplotlib.path import failed: {e}")

try:
    import pyvista as pv
    print(f"✓ PyVista imported successfully. Version: {pv.__version__}")
except Exception as e:
    print(f"✗ PyVista import failed: {e}")

try:
    from PyQt5.QtWidgets import QApplication
    print("✓ PyQt5 imported successfully")
except Exception as e:
    print(f"✗ PyQt5 import failed: {e}")

print("Import test completed.")
