import sys
import os
import numpy as np
import ezdxf
import pyvista as pv
import open3d as o3d
import copy
import re
import collections
import time
import math
import tetgen
from datetime import datetime
import uuid
import traceback
from PyQt5.QtWidgets import (QMainWindow, QApplication, QFileDialog, QMessageBox,
                            QPushButton, QVBoxLayout, QWidget, QLabel, QGroupBox,
                            QGridLayout, QStatusBar, QLineEdit, QCheckBox,
                            QProgressBar, QTextEdit, QSplitter, QTabWidget)
from PyQt5.QtGui import QFont, QTextCursor
from PyQt5.QtCore import Qt

pv.global_theme.allow_empty_mesh = True

# 通用的异常处理装饰器
def safe_execute(default_return=None, show_error=True):
    """
    通用的异常处理装饰器，确保方法不会导致程序闪退
    
    参数:
    default_return: 出错时返回的默认值
    show_error: 是否显示错误信息
    """
    def decorator(func):
        import functools
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                import traceback
                error_msg = f"[安全执行] 方法 {func.__name__} 执行失败: {str(e)}"
                print(error_msg)
                print(traceback.format_exc())
                
                # 如果是类方法且有add_log_message方法，则记录日志
                if args and hasattr(args[0], 'add_log_message'):
                    try:
                        args[0].add_log_message(f"错误: {func.__name__} - {str(e)}")
                    except:
                        pass
                
                # 如果需要显示错误对话框
                if show_error:
                    try:
                        from PyQt5.QtWidgets import QMessageBox, QApplication
                        app = QApplication.instance()
                        if app is not None:
                            msg = QMessageBox()
                            msg.setIcon(QMessageBox.Warning)
                            msg.setWindowTitle("操作错误")
                            msg.setText(f"操作失败，但程序将继续运行")
                            msg.setDetailedText(f"方法: {func.__name__}\n错误: {str(e)}\n\n{traceback.format_exc()}")
                            msg.setStandardButtons(QMessageBox.Ok)
                            msg.exec_()
                    except:
                        pass  # 连错误对话框都显示不了，至少程序不闪退
                
                return default_return
        
        return wrapper
    return decorator

def ear_clipping_triangulation(polygon_points, verbose=False, show_debug_plots=False):
    """
    使用耳切法对简单多边形进行三角化

    参数:
    polygon_points: 多边形的顶点数组 (N, 2)，假定为逆时针顺序 (CCW)
    verbose: 是否输出详细日志
    show_debug_plots: 是否显示调试绘图

    返回:
    三角形列表，每个三角形由三个顶点索引组成
    """
    polygon = np.array(polygon_points, dtype=np.float64)
    n = len(polygon)

    if n < 3:
        raise ValueError("多边形顶点少于3个，无法三角化")

    if verbose:
        print(f"开始耳切法三角化，多边形点数：{n}")

    signed_area_check = calculate_signed_area(polygon)
    if signed_area_check < 0:
        if verbose:
            print(
                "Input to triangulation is CW, should be CCW. Reversing internally."
            )
        polygon = polygon[::-1]
    elif signed_area_check == 0:
        raise ValueError("多边形面积为零，无法三角化 (可能共线)")

    # 特殊处理：如果只有3或4个顶点，使用简化方案
    if n == 3:
        return [[0, 1, 2]]  # 直接返回一个三角形
    elif n == 4:
        # 简单处理四边形：分成两个三角形
        return [[0, 1, 2], [0, 2, 3]]

    triangles = []
    # 存储当前有效顶点的索引列表
    vertices_indices = list(range(n))

    remaining_vertices_count = n
    current_ear_index_in_list = 0  # 指向 vertices_indices 的索引

    # 迭代直到只剩下3个顶点（形成最后一个三角形）
    while remaining_vertices_count > 3:
        found_ear = False
        search_attempts = 0
        max_search_attempts = remaining_vertices_count * 2  # 防止无限循环

        while not found_ear and search_attempts < max_search_attempts:
            prev_original_index = vertices_indices[
                (current_ear_index_in_list - 1 + remaining_vertices_count)
                % remaining_vertices_count
            ]
            curr_original_index = vertices_indices[current_ear_index_in_list]
            next_original_index = vertices_indices[
                (current_ear_index_in_list + 1 + remaining_vertices_count)
                % remaining_vertices_count
            ]
            if is_ear(polygon, current_ear_index_in_list, vertices_indices):
                if verbose:
                    print(
                        f"顶点 {curr_original_index} ({polygon[curr_original_index]}) 是一个耳朵。"
                    )
                # 找到一个耳朵，记录对应的三角形 (使用原始索引)
                triangles.append(
                    [prev_original_index, curr_original_index, next_original_index]
                )
                # 从顶点列表中移除当前耳朵顶点
                vertices_indices.pop(current_ear_index_in_list)
                remaining_vertices_count -= 1

                # 重置索引，确保下一次迭代从有效位置开始
                # 如果移除了最后一个元素，需要将索引回绕
                current_ear_index_in_list %= remaining_vertices_count
                found_ear = True
            else:
                # 当前顶点不是耳朵，检查下一个
                current_ear_index_in_list = (
                    current_ear_index_in_list + 1
                ) % remaining_vertices_count

            search_attempts += 1

        if not found_ear:
            # 如果遍历完所有顶点都没有找到耳朵，说明算法失败或多边形复杂
            print(f"耳切法未能找到耳朵，剩余顶点数: {remaining_vertices_count}")
            print(f"当前剩余顶点索引: {vertices_indices}")

            # 特殊处理：如果剩余4个顶点
            if remaining_vertices_count == 4:
                print("对剩余4个顶点应用特殊处理")
                i0, i1, i2, i3 = vertices_indices
                # 直接分割成两个三角形
                triangles.append([i0, i1, i2])
                triangles.append([i0, i2, i3])
                break

            # 尝试简单分割策略 - 如果有更多顶点
            if remaining_vertices_count > 4:
                print("使用简单分割策略")
                i0 = vertices_indices[0]
                for i in range(2, remaining_vertices_count):
                    triangles.append([i0, vertices_indices[i - 1], vertices_indices[i]])
                break

            # 如果还是失败，则放弃并报错
            raise RuntimeError("耳切法三角化失败，可能是多边形自相交或存在其他问题")

    # 添加最后一个三角形
    if remaining_vertices_count == 3:
        triangles.append(vertices_indices)

    if verbose:
        print(f"耳切法三角化完成，生成了{len(triangles)}个三角形")

    return triangles


def is_ccw(a, b, c):
    """检查三个点是否构成逆时针方向"""
    # 使用叉积判断: (b.x - a.x) * (c.y - a.y) - (b.y - a.y) * (c.x - a.x)
    cross_product = (b[0] - a[0]) * (c[1] - a[1]) - (b[1] - a[1]) * (c[0] - a[0])
    # 添加一个小的容差来处理共线情况
    is_ccw_result = cross_product > 1e-10
    return is_ccw_result


def is_point_in_triangle(p, a, b, c):
    """检查点p是否在三角形abc内部"""
    # 使用重心坐标系方法
    v0 = c - a
    v1 = b - a
    v2 = p - a

    dot00 = np.dot(v0, v0)
    dot01 = np.dot(v0, v1)
    dot02 = np.dot(v0, v2)
    dot11 = np.dot(v1, v1)
    dot12 = np.dot(v1, v2)

    # 计算重心坐标
    inv_denom = (
        1.0 / (dot00 * dot11 - dot01 * dot01)
        if (dot00 * dot11 - dot01 * dot01) != 0
        else 0
    )
    if inv_denom == 0:
        return False  # Avoid division by zero if triangle is degenerate

    u = (dot11 * dot02 - dot01 * dot12) * inv_denom
    v = (dot00 * dot12 - dot01 * dot02) * inv_denom

    # 检查点是否在三角形内（包含边界）
    return (u >= 0) and (v >= 0) and (u + v <= 1)


def is_ear(polygon, i, vertices_indices):
    """检查顶点i是否是一个"耳朵" """
    n = len(vertices_indices)
    prev_index = vertices_indices[(i - 1 + n) % n]
    curr_index = vertices_indices[i]
    next_index = vertices_indices[(i + 1) % n]

    prev_vertex = polygon[prev_index]
    curr_vertex = polygon[curr_index]
    next_vertex = polygon[next_index]

    # 1. 必须是凸顶点 (检查内部角度是否小于180度, 即是否CCW)
    if not is_ccw(prev_vertex, curr_vertex, next_vertex):
        return False

    # 2. 三角形内部不能包含其他多边形顶点
    for j in range(n):
        other_index = vertices_indices[j]
        if (
            other_index != prev_index
            and other_index != curr_index
            and other_index != next_index
        ):
            if is_point_in_triangle(
                polygon[other_index], prev_vertex, curr_vertex, next_vertex
            ):
                return False
    return True


def calculate_signed_area(polygon):
    """计算多边形的符号面积，用于判断顶点顺序（CCW > 0, CW < 0）"""
    area = 0.0
    n = len(polygon)
    for i in range(n):
        j = (i + 1) % n
        area += polygon[i][0] * polygon[j][1]
        area -= polygon[j][0] * polygon[i][1]
    return area / 2.0

def inspect_mesh(mesh, label=""):
    """检查网格状态并打印详细信息"""
    print(f"\n[MESH INFO] {label}")
    print(f"- 类型: {type(mesh).__name__}")
    print(f"- 点数量: {mesh.n_points}")
    print(f"- 单元数量: {mesh.n_cells}")

    # 检查单元类型 - 安全获取
    if hasattr(mesh, "n_cells") and mesh.n_cells > 0:
        try:
            # 尝试获取单元类型信息
            is_all_triangles = False

            # 检查是否有cells_dict属性以确定单元类型
            if hasattr(mesh, "cells_dict"):
                cell_types = list(mesh.cells_dict.keys())
                print(f"- 单元类型: {cell_types}")
                is_all_triangles = len(cell_types) == 1 and cell_types[0] == "triangle"
                print(f"- 是否全部是三角形: {is_all_triangles}")
            # 尝试另一种方式
            elif hasattr(mesh, "cell_types"):
                cell_types = np.unique(mesh.cell_types)
                print(f"- 单元类型: {cell_types}")
                # VTK_TRIANGLE = 5
                is_all_triangles = np.all(cell_types == 5)
                print(f"- 是否全部是三角形: {is_all_triangles}")
            else:
                # 如果无法直接获取，使用face_arrays尝试推断
                if hasattr(mesh, "face_arrays") and mesh.face_arrays:
                    print(f"- 可用的面数组: {list(mesh.face_arrays.keys())}")
                else:
                    print("- 无法获取单元类型信息")
        except Exception as e:
            print(f"- 无法获取单元类型: {e}")

    # 检查流形性质
    if hasattr(mesh, "is_manifold"):
        try:
            print(f"- 是否为流形: {mesh.is_manifold}")
        except:
            print("- 无法确定是否为流形")

# 从ui_o3.py的函数 - 改进版
@safe_execute(default_return=(np.array([]), np.array([])), show_error=False)
def read_tsurf_data(file_path):
    """读取TS文件（TSURF格式）数据"""
    encodings = ['utf-8', 'gbk', 'iso-8859-1'] # 尝试多种编码
    
    for encoding in encodings:
        try:
            vrtx = []
            vrtx_map = {}  # 用于映射顶点ID
            trgl = []
            current_idx = 0
            
            with open(file_path, 'r', encoding=encoding) as file:
                for line_num, line in enumerate(file, 1):
                    # 处理VRTX行
                    if 'VRTX' in line:
                        l_input = re.split(r'[\s*]', line)
                        try:
                            # 过滤掉空字符串和非数字字符串
                            nums = [x for x in l_input[2:] if x and x.replace('.', '').replace('-', '').isdigit()]
                            if len(nums) >= 3:
                                vertex_id = int(l_input[1])
                                coords = [float(nums[0]), float(nums[1]), float(nums[2])]
                                vrtx.append(coords)
                                vrtx_map[vertex_id] = current_idx
                                current_idx += 1
                        except (ValueError, IndexError) as e:
                            print(f"处理顶点时出错，行 {line_num}: {e}")
                            continue

                    # 处理ATOM行（复制已存在的顶点）
                    elif 'ATOM' in line:
                        l_input = re.split(r'[\s*]', line)
                        if len(l_input) > 2:
                            try:
                                vertex_id_atom = int(l_input[2])
                                if vertex_id_atom in vrtx_map:
                                    # 复制已存在的顶点
                                    orig_idx = vrtx_map[vertex_id_atom]
                                    vrtx.append(vrtx[orig_idx])
                                    new_id = int(l_input[1])
                                    vrtx_map[new_id] = current_idx
                                    current_idx += 1
                            except (ValueError, IndexError) as e:
                                print(f"处理ATOM时出错，行 {line_num}: {e}")
                                continue

                    # 处理TRGL行
                    elif 'TRGL' in line:
                        l_input = re.split(r'[\s*]', line)
                        try:
                            # 过滤掉空字符串和非数字字符串
                            nums = [x for x in l_input[1:] if x and x.isdigit()]
                            if len(nums) == 3:
                                # 使用映射转换顶点索引
                                v1 = vrtx_map[int(nums[0])]
                                v2 = vrtx_map[int(nums[1])]
                                v3 = vrtx_map[int(nums[2])]
                                trgl.append([v1, v2, v3])
                        except (ValueError, IndexError, KeyError) as e:
                            print(f"处理三角面时出错，行 {line_num}: {e}")
                            continue

            if len(vrtx) > 0 and len(trgl) > 0:
                print(f"成功读取 {file_path}，使用编码 {encoding}")
                print(f"读取了 {len(vrtx)} 个顶点和 {len(trgl)} 个三角面")
                return np.array(vrtx), np.array(trgl)
                
        except UnicodeDecodeError:
            print(f"无法使用 {encoding} 编码读取 {file_path}，尝试下一种编码...")
            continue
        except Exception as e:
            print(f"读取 {file_path} 时发生意外错误: {e}")
            continue
    
    print(f"尝试所有编码都无法读取 {file_path}")
    return np.array([]), np.array([])

def convert_to_vtk(points, triangles):
    faces = np.hstack([[3] + list(tri) for tri in triangles])
    mesh = pv.PolyData(points, faces)
    return mesh

def normalize_mesh_coordinates(mesh1, mesh2, verbose=True):
    """
    将两个网格的坐标标准化到原点附近，解决大坐标值导致的布尔运算精度问题
    
    参数:
    mesh1, mesh2: pyvista网格对象
    verbose: 是否输出详细信息
    
    返回:
    (mesh1_norm, mesh2_norm, offset): 标准化后的网格和偏移量
    """
    # 计算两个网格的综合边界框
    bounds1 = mesh1.bounds
    bounds2 = mesh2.bounds
    
    # 计算综合的最小值作为平移参考点
    min_x = min(bounds1[0], bounds2[0])
    min_y = min(bounds1[2], bounds2[2])
    min_z = min(bounds1[4], bounds2[4])
    
    offset = np.array([min_x, min_y, min_z])
    
    if verbose:
        print(f"坐标标准化偏移量: {offset}")
        print(f"原始网格1边界框: {bounds1}")
        print(f"原始网格2边界框: {bounds2}")
    
    # 平移网格到原点附近
    mesh1_norm = mesh1.translate(-offset, inplace=False)
    mesh2_norm = mesh2.translate(-offset, inplace=False)
    
    if verbose:
        print(f"标准化后网格1边界框: {mesh1_norm.bounds}")
        print(f"标准化后网格2边界框: {mesh2_norm.bounds}")
    
    return mesh1_norm, mesh2_norm, offset

def restore_mesh_coordinates(mesh, offset):
    """
    将网格坐标还原到原始位置
    
    参数:
    mesh: 需要还原的网格
    offset: 之前的偏移量
    
    返回:
    还原后的网格
    """
    return mesh.translate(offset, inplace=False)

def save_boolean_result_with_coordinate_restoration(self, meshset, output_obj, output_ts, result_name, coordinate_normalized, coord_offset):
    """
    保存布尔运算结果，并在需要时还原坐标
    
    参数:
    meshset: PyMeshLab的MeshSet对象
    output_obj: OBJ输出路径
    output_ts: TS输出路径
    result_name: 结果名称
    coordinate_normalized: 是否使用了坐标标准化
    coord_offset: 坐标偏移量
    
    返回:
    是否成功保存
    """
    try:
        # 保存为OBJ格式
        if self.save_mesh_manually(meshset, output_obj, result_name):
            self.add_log_message(f"已保存{result_name}结果为OBJ格式: {os.path.basename(output_obj)}")
            
            # 如果使用了坐标标准化，需要还原坐标
            if coordinate_normalized:
                self.add_log_message(f"还原{result_name}结果坐标到原始位置...")
                try:
                    # 加载布尔运算结果
                    result_mesh = pv.read(output_obj)
                    # 还原坐标
                    result_restored = restore_mesh_coordinates(result_mesh, coord_offset)
                    # 保存还原后的结果
                    restored_obj = output_obj.replace('.obj', '_restored.obj')
                    result_restored.save(restored_obj)
                    self.add_log_message(f"已还原{result_name}结果坐标: {os.path.basename(restored_obj)}")
                    
                    # 使用还原后的文件转换为TS格式
                    if self.obj_to_ts(restored_obj, output_ts, name=result_name):
                        self.add_log_message(f"已转换并保存{result_name}结果为TS格式: {os.path.basename(output_ts)}")
                    else:
                        self.add_log_message(f"警告: {result_name}结果转换为TS格式失败")
                    return True
                    
                except Exception as restore_error:
                    self.add_log_message(f"坐标还原失败: {restore_error}，使用原始结果")
                    # 使用原始结果转换为TS格式
                    if self.obj_to_ts(output_obj, output_ts, name=result_name):
                        self.add_log_message(f"已转换并保存{result_name}结果为TS格式: {os.path.basename(output_ts)}")
                    else:
                        self.add_log_message(f"警告: {result_name}结果转换为TS格式失败")
                    return True
            else:
                # 直接转换为TS格式
                if self.obj_to_ts(output_obj, output_ts, name=result_name):
                    self.add_log_message(f"已转换并保存{result_name}结果为TS格式: {os.path.basename(output_ts)}")
                else:
                    self.add_log_message(f"警告: {result_name}结果转换为TS格式失败")
                return True
        else:
            self.add_log_message(f"警告: 手动保存{result_name}结果失败")
            return False
    except Exception as e:
        self.add_log_message(f"保存{result_name}结果时发生错误: {e}")
        return False



@safe_execute(default_return=[], show_error=False)
def read_dxf_boundary(dxf_file):
    """读取DXF文件中的多段线边界，支持多个多段线"""
    doc = ezdxf.readfile(dxf_file)
    msp = doc.modelspace()

    # 存储多个多段线的列表
    polylines = []

    # 提取所有LWPOLYLINE
    for entity in msp.query("LWPOLYLINE"):
        boundary_points = []
        points = entity.get_points()
        # 为每个点分配ID并转换为3D点
        for p in points:
            boundary_points.append((p[0], p[1], 0.0))
        if len(boundary_points) > 2:  # 至少需要3个点才能构成有效环
            polylines.append(np.array(boundary_points))
    # 提取所有3D POLYLINE
    for entity in msp.query("POLYLINE"):
        boundary_points = []
        vertices = list(entity.vertices)
        for vertex in vertices:
            boundary_points.append((vertex.dxf.location.x, vertex.dxf.location.y, 0.0))

        if len(boundary_points) > 2:  # 至少需要3个点才能构成有效环
            polylines.append(np.array(boundary_points))
    # 返回多个多段线
    return polylines

def order_ring(points: np.ndarray) -> np.ndarray:
    """
    输入: 一圈无序的底环点 (N,3)
    输出: 按顺（逆）时针排序后的点
    """
    # 用质心+极角排序，适合凸/微凹边界
    center = points.mean(axis=0)
    angles = np.arctan2(points[:, 1] - center[1], points[:, 0] - center[0])
    order = np.argsort(angles)

    # 获取处理结果文件夹路径 (如果在类函数内调用，将有self.output_folder)
    output_folder = (
        getattr(IntegratedProcessor.instance, "output_folder", None)
        if hasattr(IntegratedProcessor, "instance")
        else None
    )

    # 如果有输出文件夹，保存到输出文件夹，否则保存到当前目录
    debug_path = (
        os.path.join(output_folder, "ordered_ring_debug.txt")
        if output_folder
        else "ordered_ring_debug.txt"
    )

    # 调试输出
    with open(debug_path, "w") as f:
        f.write(f"排序前点数: {len(points)}\n")
        f.write(f"质心: ({center[0]:.2f}, {center[1]:.2f}, {center[2]:.2f})\n\n")
        f.write("排序后的点坐标:\n")
        for i, p in enumerate(points[order]):
            f.write(
                f"{i}: ({p[0]:.2f}, {p[1]:.2f}, {p[2]:.2f}) 角度: {angles[order[i]]:.2f}\n"
            )

    return points[order]


def build_side_wall_surface(ring: np.ndarray, height: float) -> pv.PolyData:
    """
    创建侧壁面

    参数:
    ring: (N,3) 按顺/逆时针排序的底环点
    height: 侧壁高度

    返回:
    侧壁四边形面的PolyData对象
    """
    n = ring.shape[0]
    if n < 3:
        raise ValueError("边界点不足 3，无法成面")

    # 创建顶环 - 将底环点向上平移height距离
    top_ring = ring.copy()
    top_ring[:, 2] += height

    # 合并上下环点
    all_points = np.vstack([ring, top_ring])

    # 创建四边形面
    faces = []
    for i in range(n):
        # 底环的当前点和下一点
        b1 = i
        b2 = (i + 1) % n  # 循环回到起点

        # 顶环对应的两点
        t1 = b1 + n
        t2 = b2 + n

        # 添加四边形面 (b1,b2,t2,t1)
        faces.append(4)  # 4个点的面
        faces.extend([b1, b2, t2, t1])

    # 调试信息
    print(f"创建侧壁: {n}个四边形, 总点数: {len(all_points)}")

    # 创建PolyData
    side_wall = pv.PolyData(all_points, np.array(faces))
    return side_wall


def make_tall_side_prism(ring: np.ndarray, height: float) -> pv.PolyData:
    """
    创建一个高侧壁棱柱，用于后续布尔裁剪

    参数:
    ring: (N,3) 按顺/逆时针排序的底环点
    height: 侧壁高度

    返回:
    高侧壁棱柱的PolyData对象
    """
    try:
        # 创建侧壁
        side_wall = build_side_wall_surface(ring, height)

        # 提取上下环点
        points = np.array(side_wall.points)
        n = len(ring)
        bottom_ring = points[:n]
        top_ring = points[n : 2 * n]

        # 为底环和顶环创建三角面
        bottom_faces = []
        top_faces = []

        # 将三维点投影到XY平面用于三角化
        polygon_2d_bottom = bottom_ring[:, :2]
        polygon_2d_top = top_ring[:, :2]

        # --- 检查并确保顶点顺序为逆时针 (CCW) ---
        signed_area = calculate_signed_area(polygon_2d_bottom)
        ccw = signed_area > 0

        print(f"底面多边形有符号面积: {signed_area}")

        if not ccw and signed_area < 0:
            # 顺时针，需要反转点的顺序用于三角化
            print("多边形顶点顺序为顺时针(CW)，反转为逆时针(CCW)用于三角化")
            bottom_indices_for_triangulation = list(range(len(bottom_ring)))[::-1]
            top_indices_for_triangulation = list(range(len(top_ring)))[::-1]

            # 反转后的点
            bottom_points_for_triangulation = bottom_ring[
                bottom_indices_for_triangulation
            ]
            top_points_for_triangulation = top_ring[top_indices_for_triangulation]

            # 反转后的2D投影
            polygon_2d_bottom = bottom_points_for_triangulation[:, :2]
            polygon_2d_top = top_points_for_triangulation[:, :2]
        else:
            # 已经是CCW或零面积
            print("多边形顶点顺序为逆时针(CCW)或零面积，保持原样")
            bottom_indices_for_triangulation = list(range(len(bottom_ring)))
            bottom_points_for_triangulation = bottom_ring
            top_indices_for_triangulation = list(range(len(top_ring)))
            top_points_for_triangulation = top_ring

        # 使用耳切法进行三角化
        try:
            bottom_triangles = ear_clipping_triangulation(
                polygon_2d_bottom, verbose=True, show_debug_plots=False
            )
            print(f"底面三角化成功，得到 {len(bottom_triangles)} 个三角形")

            # 转换为面列表格式
            for triangle in bottom_triangles:
                # 映射回原始索引
                original_indices = [
                    bottom_indices_for_triangulation[idx] for idx in triangle
                ]
                bottom_faces.extend([3] + original_indices)

            # 对顶面进行三角化
            top_triangles = ear_clipping_triangulation(
                polygon_2d_top, verbose=True, show_debug_plots=False
            )
            print(f"顶面三角化成功，得到 {len(top_triangles)} 个三角形")

            # 转换为面列表格式
            for triangle in top_triangles:
                # 映射回原始索引并添加偏移
                original_indices = [
                    top_indices_for_triangulation[idx] + n for idx in triangle
                ]
                top_faces.extend([3] + original_indices)

        except Exception as e:
            print(f"耳切法三角化失败: {e}")
            print("回退到简单扇形三角化...")

            # 备选方案: 简单扇形三角化
            for i in range(1, n - 1):
                bottom_faces.append(3)  # 3个点的面
                bottom_faces.extend([0, i, i + 1])

                top_faces.append(3)  # 3个点的面
                top_faces.extend([n, n + i, n + i + 1])

        # 将侧壁、底面和顶面合并
        all_faces = np.concatenate([side_wall.faces, bottom_faces, top_faces])

        # 创建完整的棱柱体
        prism = pv.PolyData(side_wall.points, all_faces)

        return prism

    except Exception as e:
        print(f"创建高侧壁棱柱时发生错误: {e}")
        # 如果出错，返回侧壁作为备选
        return build_side_wall_surface(ring, height)


def create_boundary_from_closed_polyline(points, height, output_dir=None, verbose=True):
    """
    使用闭合多段线创建边界体

    参数:
    points: 多段线的点 (N,3)
    height: 拉伸高度
    output_dir: 输出目录
    verbose: 是否输出详细日志

    返回:
    边界体的PolyData对象
    """
    if verbose:
        print(f"使用闭合多段线创建边界体，点数: {len(points)}")

    # 使用点集作为底面
    # 确保Z坐标相同 (如果已经在XY平面上则无需操作)
    bottom_points = np.array(points, dtype=np.float64)
    if not np.allclose(bottom_points[:, 2], bottom_points[0, 2]):
        bottom_points[:, 2] = np.mean(bottom_points[:, 2])  # 使用平均Z值

    # 创建顶面点 - 复制底面点并加上高度
    top_points = bottom_points.copy()
    top_points[:, 2] += height

    # 创建完整的点集 - 先底面点，后顶面点
    all_points = np.vstack([bottom_points, top_points])

    # --- 构建 Faces 数组 (改进版) ---
    faces_list = []
    max_index = 2 * len(bottom_points) - 1

    # 1. 侧面 (Side faces - Quads)
    for i in range(len(bottom_points)):
        b1 = i
        b2 = (i + 1) % len(bottom_points)
        t1 = i + len(bottom_points)
        t2 = (i + 1) % len(bottom_points) + len(bottom_points)
        quad_indices = [b1, b2, t2, t1]
        if any(idx > max_index or idx < 0 for idx in quad_indices):
            print(
                f"Invalid index in side quad {i}: {quad_indices} (max={max_index})"
            )
            raise ValueError("Invalid vertex index generated for side faces")
        faces_list.extend([4] + quad_indices)  # Add count (4) then indices

    # --- 检查并确保顶点顺序为逆时针 (CCW) ---
    polygon_2d_for_check = bottom_points[:, :2]  # 使用底面点进行检查
    signed_area = calculate_signed_area(polygon_2d_for_check)

    ccw = signed_area > 0
    if not ccw:
        if signed_area < 0:
            # 顺时针，需要反转点的顺序
            bottom_indices_for_triangulation = list(range(len(bottom_points)))[::-1]
            bottom_points_for_triangulation = bottom_points[
                bottom_indices_for_triangulation
            ]
            top_indices_for_triangulation = list(range(len(bottom_points)))[::-1]
            top_points_for_triangulation = top_points[top_indices_for_triangulation]
        else:
            # 零面积或点共线，保持原样
            bottom_indices_for_triangulation = list(range(len(bottom_points)))
            bottom_points_for_triangulation = bottom_points
            top_indices_for_triangulation = list(range(len(bottom_points)))
            top_points_for_triangulation = top_points
    else:
        # 已经是CCW, 保持点的顺序
        bottom_indices_for_triangulation = list(range(len(bottom_points)))
        bottom_points_for_triangulation = bottom_points
        top_indices_for_triangulation = list(range(len(bottom_points)))
        top_points_for_triangulation = top_points

    # --- 对底面进行三角化 ---
    # 将三维点投影到XY平面用于三角化
    polygon_2d = bottom_points_for_triangulation[:, :2]

    # 使用改进的耳切法进行三角化
    triangles = ear_clipping_triangulation(
        polygon_2d, verbose=verbose, show_debug_plots=False
    )

    if verbose:
        print(f"底面三角化成功，得到 {len(triangles)} 个三角形")

    # --- 对顶面进行三角化 ---
    polygon_2d_top = top_points_for_triangulation[:, :2]
    triangles_top = ear_clipping_triangulation(
        polygon_2d_top, verbose=verbose, show_debug_plots=False
    )

    if verbose:
        print(f"顶面三角化成功，得到 {len(triangles_top)} 个三角形")

    # --- 构建完整的面数组 ---

    # 2. 底面 (使用三角化结果)
    bottom_faces_start_idx = len(faces_list)
    for triangle in triangles:
        # 映射回原始索引
        original_indices = [bottom_indices_for_triangulation[idx] for idx in triangle]
        faces_list.extend([3] + original_indices)

    # 3. 顶面 (使用三角化结果)
    top_faces_start_idx = len(faces_list)
    for triangle in triangles_top:
        # 映射回原始索引并添加偏移
        original_indices = [
            top_indices_for_triangulation[idx] + len(bottom_points) for idx in triangle
        ]
        faces_list.extend([3] + original_indices)

    # 创建面数组为 vtk 格式
    all_faces = np.array(faces_list, dtype=np.int64)

    # 创建 PolyData 对象
    polydata = pv.PolyData(all_points, all_faces)

    # 如果提供了输出目录，保存VTK文件
    if output_dir:
        output_file = os.path.join(output_dir, "boundary_body.vtk")
        polydata.save(output_file)
        if verbose:
            print(f"已保存边界体到: {output_file}")

    return polydata


def create_side_walls_from_boundary(
    mesh: pv.PolyData, boundary_body: pv.PolyData, height: float = 3000.0
) -> pv.PolyData:
    """
    从边界体创建侧壁

    参数:
    mesh: 原始网格
    boundary_body: 边界体
    height: 侧壁高度

    返回:
    添加了侧壁的网格
    """
    # 提取边界环
    print("正在提取边界环...")
    boundary_edges = boundary_body.extract_feature_edges(
        boundary_edges=True, feature_edges=False, manifold_edges=False
    )

    # 获取边缘点和边缘线
    edge_points = np.array(boundary_edges.points)
    edge_lines = np.array(boundary_edges.lines)

    # 保存边缘线数据用于调试
    debug_edges_structure(edge_lines, edge_points, "boundary_edges_debug.txt")

    print(f"找到边界边缘，边缘点数: {len(edge_points)}")

    # 提取闭合环
    i = 0
    rings = []
    while i < len(edge_lines):
        n_points = edge_lines[i]
        if n_points >= 3:  # 至少需要3个点才能构成有效环
            point_ids = edge_lines[i + 1 : i + 1 + n_points]
            ring_points = edge_points[point_ids]
            rings.append(ring_points)
            print(f"发现边界环，包含 {n_points} 个点")
        i += n_points + 1

    if not rings:
        print("警告: 无法找到有效的边界环")
        return mesh

    print(f"共找到 {len(rings)} 个边界环")

    # 为每个环创建侧壁
    all_walls = []
    for i, ring in enumerate(rings):
        try:
            # 排序环点
            ordered_ring = order_ring(ring)
            print(f"环 {i+1}: 排序后包含 {len(ordered_ring)} 个点")

            # 创建侧壁
            wall = make_tall_side_prism(ordered_ring, height)
            all_walls.append(wall)
            print(f"已为环 {i+1} 创建侧壁")
        except Exception as e:
            print(f"为环 {i+1} 创建侧壁时发生错误: {e}")

    if not all_walls:
        print("警告: 无法创建任何侧壁")
        return mesh

    # 合并所有侧壁
    try:
        combined_walls = all_walls[0]
        for wall in all_walls[1:]:
            combined_walls = combined_walls.merge(wall)
        print(
            f"成功合并所有侧壁，总点数: {combined_walls.n_points}, 总面数: {combined_walls.n_cells}"
        )

        # 合并原始网格和侧壁
        result = mesh.merge(combined_walls)
        print(
            f"成功将侧壁合并到原始网格，总点数: {result.n_points}, 总面数: {result.n_cells}"
        )

        return result
    except Exception as e:
        print(f"合并侧壁时发生错误: {e}")
        return mesh


def debug_edges_structure(edge_lines, edge_points, file_path="edge_debug.txt"):
    """调试函数：将边缘线结构详细写入文件"""
    with open(file_path, "w") as f:
        f.write(f"边缘点总数: {len(edge_points)}\n")
        f.write(f"边缘线数组长度: {len(edge_lines)}\n\n")

        f.write("边缘点坐标:\n")
        for i, point in enumerate(edge_points):
            f.write(f"{i}: ({point[0]:.2f}, {point[1]:.2f}, {point[2]:.2f})\n")

        f.write("\n边缘线结构:\n")
        i = 0
        line_count = 0
        while i < len(edge_lines):
            n_points = edge_lines[i]
            f.write(f"线段 {line_count} - 点数: {n_points}\n")

            if n_points >= 2:
                point_ids = edge_lines[i + 1 : i + 1 + n_points]
                f.write(f"  点ID: {point_ids}\n")

                # 添加点坐标输出
                f.write("  点坐标:\n")
                for pid in point_ids:
                    p = edge_points[pid]
                    f.write(f"    {pid}: ({p[0]:.2f}, {p[1]:.2f}, {p[2]:.2f})\n")

            i += n_points + 1
            line_count += 1

        f.write(f"\n总线段数: {line_count}\n")


class IntegratedProcessor(QMainWindow):
    """集成处理器，整合了所有功能"""

    def __init__(self):
        """安全的构造函数，确保即使出现错误也不会闪退"""
        try:
            super().__init__()
            print("[初始化] 开始初始化主窗口...")

            # 存储实例引用以便visualize_and_save函数可以访问处理模式
            IntegratedProcessor.instance = self

            # 安全初始化变量
            try:
                self.initializeVariables()
                print("[初始化] 变量初始化完成")
            except Exception as var_error:
                print(f"[初始化警告] 变量初始化失败: {var_error}")
                # 设置最基本的变量以避免后续引用错误
                self.output_folder = None
                self.warning_count = 0
                self.add_log_message = lambda msg: print(f"[日志] {msg}")

            # 安全初始化UI
            try:
                self.initUI()
                print("[初始化] UI初始化完成")
            except Exception as ui_error:
                print(f"[初始化失败] UI初始化失败: {ui_error}")
                import traceback
                print(traceback.format_exc())
                
                # 创建最基本的错误显示UI
                self.create_minimal_error_ui(ui_error)
                
        except Exception as init_error:
            print(f"[严重错误] 构造函数失败: {init_error}")
            import traceback
            print(traceback.format_exc())
            raise  # 重新抛出，让上层处理
            
    def create_minimal_error_ui(self, error):
        """创建最基本的错误显示UI"""
        try:
            from PyQt5.QtWidgets import QVBoxLayout, QLabel, QTextEdit, QPushButton, QWidget
            
            self.setWindowTitle("采空区处理工具 - 初始化错误")
            self.setGeometry(100, 100, 600, 400)
            
            central_widget = QWidget()
            self.setCentralWidget(central_widget)
            
            layout = QVBoxLayout(central_widget)
            
            error_label = QLabel("程序初始化时遇到错误，但已安全启动。您可以查看错误详情：")
            error_label.setWordWrap(True)
            layout.addWidget(error_label)
            
            error_text = QTextEdit()
            error_text.setPlainText(f"错误信息：{str(error)}\n\n程序仍可运行，但某些功能可能受限。")
            error_text.setReadOnly(True)
            layout.addWidget(error_text)
            
            retry_button = QPushButton("重试初始化UI")
            retry_button.clicked.connect(self.retry_ui_init)
            layout.addWidget(retry_button)
            
            print("[初始化] 创建了最基本的错误显示UI")
            
        except Exception as minimal_error:
            print(f"[严重错误] 连最基本的错误UI都无法创建: {minimal_error}")
            
    def retry_ui_init(self, checked=False):
        """重试UI初始化"""
        try:
            print("[重试] 尝试重新初始化UI...")
            # 清除当前内容
            if hasattr(self, 'centralWidget'):
                self.centralWidget().deleteLater()
            
            # 重新初始化
            self.initUI()
            print("[重试] UI重新初始化成功")
        except Exception as retry_error:
            print(f"[重试失败] UI重新初始化失败: {retry_error}")
            import traceback
            print(traceback.format_exc())

    def initializeVariables(self):
        """初始化变量"""
        self.tsurf_file = None  # TS文件路径
        self.dxf_file = None  # DXF文件路径
        self.tsurf_mesh = None  # TS文件生成的网格
        self.boundary_points = None  # 边界点
        self.boundary_mesh = None  # 边界网格
        self.result_mesh = None  # 结果网格
        
        # 新增：第二个TS文件相关变量
        self.tsurf_file2 = None  # 第二个TS文件路径
        self.tsurf_mesh2 = None  # 第二个TS文件生成的网格

        # 新增：TS模型与面运算相关变量
        self.ts_model_for_surface_op_file = None
        self.ts_surface_file = None
        
        # 新增：TS模型闭合相关变量
        self.ts_closure_file = None  # 用于闭合的TS文件路径

        # 用于保存处理结果的路径 - 使用相对路径，稍后会根据输出文件夹构建完整路径
        self.boundary_body_path = "boundary_body.vtk"  # 边界体保存路径

        # 在initializeVariables方法中添加标志
        self.is_step_by_step = True  # 默认为分步处理模式
        self.output_folder = None  # 统一输出文件夹
        self.warning_count = 0  # 添加警告计数器

    def initUI(self):
        """初始化UI"""
        self.setWindowTitle("采空区处理集成工具")
        self.setGeometry(100, 100, 900, 650)  # 稍微调整窗口大小

        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # 创建主布局 - 减小间距
        main_layout = QVBoxLayout(central_widget)
        main_layout.setSpacing(8)  # 减小间距
        main_layout.setContentsMargins(10, 10, 10, 10)  # 减小边距

        # 创建标题标签 - 增大字体
        title_label = QLabel("采空区处理集成工具")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setFont(QFont("Arial", 18, QFont.Bold))  # 从14增大到18
        title_label.setStyleSheet("color: #2c3e50; margin: 8px;")  # 增大间距
        main_layout.addWidget(title_label)
        
        # 添加程序特性提示
        info_label = QLabel("💡 程序特性：遇到错误不会自动退出，方便查看日志和检查输出文件")
        info_label.setAlignment(Qt.AlignCenter)
        info_label.setFont(QFont("Arial", 11))
        info_label.setStyleSheet("color: #27ae60; margin: 5px; background-color: #eafaf1; border: 1px solid #27ae60; border-radius: 3px; padding: 5px;")
        main_layout.addWidget(info_label)

        # 创建选项卡布局
        tab_widget = QTabWidget()
        tab_widget.setStyleSheet("""
            QTabWidget::pane {
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                background-color: white;
            }
            QTabBar {
                margin-bottom: -2px; /* Pull the pane up to connect with tab bar */
            }
            QTabBar::tab {
                background: #ecf0f1;
                border: 1px solid #bdc3c7;
                padding: 12px 25px;
                margin-right: 5px;
                border-top-left-radius: 6px;
                border-top-right-radius: 6px;
                font-weight: bold;
                font-size: 14px;
                min-width: 80px;
            }
            QTabBar::tab:selected {
                background: #3498db;
                color: white;
            }
            QTabBar::tab:hover {
                background: #85c1e9;
            }
        """)

        # === 选项卡1: 基础处理 ===
        basic_tab = QWidget()
        basic_layout = QVBoxLayout(basic_tab)
        basic_layout.setSpacing(10)  # 增大间距
        basic_layout.setContentsMargins(15, 15, 15, 15)  # 增大边距

        # 文件输入组 - 紧凑设计
        file_group = QGroupBox("📁 文件输入")
        file_group.setStyleSheet("QGroupBox { font-weight: bold; color: #2980b9; font-size: 14px; }")
        file_layout = QGridLayout()
        file_layout.setSpacing(8)  # 增大间距

        # 创建文件输入按钮 - 增大尺寸
        self.btn_open_tsurf = QPushButton("选择TS模型")
        self.btn_open_dxf = QPushButton("选择DXF边界")
        self.btn_select_output = QPushButton("选择输出文件夹")
        
        # 设置按钮样式 - 蓝色系，增大字体
        for btn in [self.btn_open_tsurf, self.btn_open_dxf, self.btn_select_output]:
            btn.setMaximumHeight(40)  # 从35增大到40
            btn.setStyleSheet("""
                QPushButton {
                    background-color: #2980b9;
                    color: white;
                    font-weight: bold;
                    border: none;
                    border-radius: 4px;
                    padding: 8px 15px;
                    font-size: 14px;
                }
                QPushButton:hover { background-color: #21618c; }
                QPushButton:pressed { background-color: #1b4f72; }
            """)

        # 添加工具提示
        self.btn_open_tsurf.setToolTip("选择并加载三角面网格模型")
        self.btn_open_dxf.setToolTip("选择并加载采空区边界数据")
        self.btn_select_output.setToolTip("选择所有结果输出的文件夹")

        # 连接信号
        self.btn_open_tsurf.clicked.connect(self.open_tsurf_file)
        self.btn_open_dxf.clicked.connect(self.open_dxf_file)
        self.btn_select_output.clicked.connect(self.open_output_folder)

        # 布局
        file_layout.addWidget(self.btn_open_tsurf, 0, 0)
        file_layout.addWidget(self.btn_open_dxf, 0, 1)
        file_layout.addWidget(self.btn_select_output, 0, 2)
        file_group.setLayout(file_layout)
        basic_layout.addWidget(file_group)

        # 参数设置组 - 紧凑设计
        params_group = QGroupBox("⚙️ 参数设置")
        params_group.setStyleSheet("QGroupBox { font-weight: bold; color: #2980b9; font-size: 14px; }")
        params_layout = QGridLayout()
        params_layout.setSpacing(8)  # 增大间距

        # 参数控件 - 增大尺寸
        self.height_label = QLabel("顶面Z:")
        self.height_input = QLineEdit("2000.0")
        self.bottom_z_label = QLabel("底面Z:")
        self.bottom_z_input = QLineEdit("-500.0")
        self.fix_iterations_label = QLabel("修复次数:")
        self.fix_iterations_input = QLineEdit("5")
        self.watertight_label = QLabel("修复水密性:")
        self.watertight_checkbox = QCheckBox()
        self.watertight_checkbox.setChecked(True)

        # 设置输入框样式 - 蓝色系，增大字体
        for input_widget in [self.height_input, self.bottom_z_input, self.fix_iterations_input]:
            input_widget.setMaximumHeight(30)  # 从25增大到30
            input_widget.setMaximumWidth(100)  # 从80增大到100
            input_widget.setStyleSheet("""
                QLineEdit {
                    padding: 5px;
                    border: 2px solid #2980b9;
                    border-radius: 3px;
                    font-size: 13px;
                    background-color: #ebf3fd;
                    color: #1b4f72;
                }
                QLineEdit:focus {
                    border-color: #3498db;
                    background-color: #d6eaf8;
                }
            """)

        # 设置标签样式 - 蓝色系，增大字体
        for label in [self.height_label, self.bottom_z_label, self.fix_iterations_label, self.watertight_label]:
            label.setStyleSheet("""
                QLabel {
                    color: #1b4f72;
                    font-weight: bold;
                    font-size: 13px;
                }
            """)

        # 布局 - 4列布局更紧凑
        params_layout.addWidget(self.height_label, 0, 0, alignment=Qt.AlignRight)
        params_layout.addWidget(self.height_input, 0, 1)
        params_layout.addWidget(self.bottom_z_label, 0, 2, alignment=Qt.AlignRight)
        params_layout.addWidget(self.bottom_z_input, 0, 3)
        params_layout.addWidget(self.fix_iterations_label, 1, 0, alignment=Qt.AlignRight)
        params_layout.addWidget(self.fix_iterations_input, 1, 1)
        params_layout.addWidget(self.watertight_label, 1, 2, alignment=Qt.AlignRight)
        params_layout.addWidget(self.watertight_checkbox, 1, 3)

        # 设置列的拉伸因子，将所有控件推向左侧
        params_layout.setColumnStretch(4, 1)

        params_group.setLayout(params_layout)
        basic_layout.addWidget(params_group)

        # 分步处理组 - 使用绿色系，增大字体
        steps_group = QGroupBox("🔧 分步处理")
        steps_group.setStyleSheet("QGroupBox { font-weight: bold; color: #27ae60; font-size: 14px; }")
        steps_layout = QGridLayout()
        steps_layout.setSpacing(8)  # 增大间距

        # 分步处理按钮
        self.btn_create_boundary = QPushButton("1. 单边界处理")
        self.btn_create_boundary_tetra = QPushButton("2. 多工作面处理")
        self.btn_boolean_ops = QPushButton("3. 布尔运算")
        self.btn_flip_normals = QPushButton("4. 反转法线")

        # 设置绿色系按钮样式，增大字体
        step_buttons = [
            (self.btn_create_boundary, "#27ae60"),
            (self.btn_create_boundary_tetra, "#28b463"),
            (self.btn_boolean_ops, "#2ecc71"),
            (self.btn_flip_normals, "#58d68d")
        ]

        for btn, color in step_buttons:
            btn.setMaximumHeight(40)  # 从35增大到40
            btn.setStyleSheet(f"""
                QPushButton {{
                    background-color: {color};
                    color: white;
                    font-weight: bold;
                    border: none;
                    border-radius: 4px;
                    padding: 8px 10px;
                    font-size: 13px;
                }}
                QPushButton:hover {{ 
                    background-color: #1e8449; 
                }}
                QPushButton:pressed {{ 
                    background-color: #186a3b; 
                }}
            """)

        # 添加工具提示
        self.btn_create_boundary.setToolTip("使用单一边界处理方法")
        self.btn_create_boundary_tetra.setToolTip("使用四面体网格处理方法")
        self.btn_boolean_ops.setToolTip("执行布尔运算")
        self.btn_flip_normals.setToolTip("翻转法线")

        # 连接信号
        self.btn_create_boundary.clicked.connect(self.create_boundary_body)
        self.btn_create_boundary_tetra.clicked.connect(self.create_boundary_body_tetra)
        self.btn_boolean_ops.clicked.connect(self.perform_boolean_operations)
        self.btn_flip_normals.clicked.connect(self.flip_normals_and_export_ts)

        # 2x2 布局
        steps_layout.addWidget(self.btn_create_boundary, 0, 0)
        steps_layout.addWidget(self.btn_create_boundary_tetra, 0, 1)
        steps_layout.addWidget(self.btn_boolean_ops, 1, 0)
        steps_layout.addWidget(self.btn_flip_normals, 1, 1)

        steps_group.setLayout(steps_layout)
        basic_layout.addWidget(steps_group)

        # 一键处理组 - 绿色系，增大字体
        oneclick_group = QGroupBox("⚡ 一键处理")
        oneclick_group.setStyleSheet("QGroupBox { font-weight: bold; color: #27ae60; font-size: 14px; }")
        oneclick_layout = QVBoxLayout()
        oneclick_layout.setSpacing(8)  # 增大间距

        self.btn_run_all = QPushButton("🚀 运行完整处理流程")
        self.btn_run_all.setMaximumHeight(45)  # 从40增大到45
        self.btn_run_all.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                font-weight: bold;
                border: none;
                border-radius: 6px;
                padding: 12px;
                font-size: 15px;
            }
            QPushButton:hover { background-color: #1e8449; }
            QPushButton:pressed { background-color: #186a3b; }
        """)
        self.btn_run_all.clicked.connect(self.run_all_process)

        # 进度条 - 绿色系，增大字体
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)
        self.progress_bar.setMaximumHeight(25)  # 从20增大到25
        self.progress_bar.setStyleSheet("""
            QProgressBar {
                border: 2px solid #27ae60;
                border-radius: 3px;
                text-align: center;
                font-size: 13px;
                background-color: #eafaf1;
                color: #186a3b;
            }
            QProgressBar::chunk {
                background-color: #27ae60;
                border-radius: 2px;
            }
        """)

        oneclick_layout.addWidget(self.btn_run_all)
        oneclick_layout.addWidget(self.progress_bar)
        oneclick_group.setLayout(oneclick_layout)
        basic_layout.addWidget(oneclick_group)

        # 添加弹性空间
        basic_layout.addStretch()
        tab_widget.addTab(basic_tab, "基础处理")

        # === 选项卡2: 高级运算 ===
        advanced_tab = QWidget()
        advanced_layout = QVBoxLayout(advanced_tab)
        advanced_layout.setSpacing(10)  # 增大间距
        advanced_layout.setContentsMargins(15, 15, 15, 15)  # 增大边距

        # TS模型间运算组 - 绿色系，增大字体
        ts_boolean_group = QGroupBox("🔄 TS模型间布尔运算")
        ts_boolean_group.setStyleSheet("QGroupBox { font-weight: bold; color: #27ae60; font-size: 14px; }")
        ts_boolean_layout = QGridLayout()
        ts_boolean_layout.setSpacing(8)  # 增大间距

        self.btn_open_ts1 = QPushButton("选择TS模型1")
        self.btn_open_ts2 = QPushButton("选择TS模型2")
        self.btn_select_ts_output = QPushButton("选择输出文件夹")
        self.btn_boolean_ts = QPushButton("执行TS模型布尔运算")

        # 设置按钮样式 - 输入部分蓝色，增大字体
        input_buttons = [self.btn_open_ts1, self.btn_open_ts2, self.btn_select_ts_output]
        for btn in input_buttons:
            btn.setMaximumHeight(35)  # 从30增大到35
            btn.setStyleSheet("""
                QPushButton {
                    background-color: #2980b9;
                    color: white;
                    font-weight: bold;
                    border: none;
                    border-radius: 4px;
                    padding: 6px 12px;
                    font-size: 13px;
                }
                QPushButton:hover { background-color: #21618c; }
                QPushButton:pressed { background-color: #1b4f72; }
            """)

        self.btn_boolean_ts.setMaximumHeight(40)  # 从35增大到40
        self.btn_boolean_ts.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                font-weight: bold;
                border: none;
                border-radius: 4px;
                padding: 10px;
                font-size: 14px;
            }
            QPushButton:hover { background-color: #1e8449; }
            QPushButton:pressed { background-color: #186a3b; }
        """)

        # 连接信号
        self.btn_open_ts1.clicked.connect(self.open_ts1_file)
        self.btn_open_ts2.clicked.connect(self.open_ts2_file)
        self.btn_select_ts_output.clicked.connect(self.open_output_folder)
        self.btn_boolean_ts.clicked.connect(self.perform_ts_boolean_operations)

        # 布局
        ts_boolean_layout.addWidget(self.btn_open_ts1, 0, 0)
        ts_boolean_layout.addWidget(self.btn_open_ts2, 0, 1)
        ts_boolean_layout.addWidget(self.btn_select_ts_output, 0, 2)
        ts_boolean_layout.addWidget(self.btn_boolean_ts, 1, 0, 1, 3)

        ts_boolean_group.setLayout(ts_boolean_layout)
        advanced_layout.addWidget(ts_boolean_group)

        # TS模型与面运算组 - 绿色系，增大字体
        ts_surface_group = QGroupBox("🔺 TS模型与面布尔运算")
        ts_surface_group.setStyleSheet("QGroupBox { font-weight: bold; color: #27ae60; font-size: 14px; }")
        ts_surface_layout = QGridLayout()
        ts_surface_layout.setSpacing(8)  # 增大间距

        self.btn_open_ts_model_for_surface_op = QPushButton("选择TS模型")
        self.btn_open_ts_surface = QPushButton("选择TS面")
        self.btn_select_ts_surface_output = QPushButton("选择输出文件夹")
        
        # 添加厚度参数设置
        self.thickness_label = QLabel("挤出厚度:")
        self.thickness_input = QLineEdit("1")
        self.thickness_input.setMaximumHeight(30)
        self.thickness_input.setMaximumWidth(100)
        self.thickness_input.setToolTip("TS面挤出为实体时的厚度参数\n正值：沿法向向上挤出\n负值：沿法向向下挤出\n（推荐范围：-2.0 到 2.0，默认1.0）")
        self.thickness_input.setStyleSheet("""
            QLineEdit {
                padding: 5px;
                border: 2px solid #27ae60;
                border-radius: 3px;
                font-size: 13px;
                background-color: #eafaf1;
                color: #186a3b;
            }
            QLineEdit:focus {
                border-color: #2ecc71;
                background-color: #d5f4e6;
            }
        """)
        
        self.thickness_label.setStyleSheet("""
            QLabel {
                color: #186a3b;
                font-weight: bold;
                font-size: 13px;
            }
        """)
        
        self.btn_boolean_ts_surface = QPushButton("执行模型与面布尔运算")

        # 设置按钮样式 - 输入部分蓝色，增大字体
        surface_input_buttons = [self.btn_open_ts_model_for_surface_op, self.btn_open_ts_surface, self.btn_select_ts_surface_output]
        for btn in surface_input_buttons:
            btn.setMaximumHeight(35)  # 从30增大到35
            btn.setStyleSheet("""
                QPushButton {
                    background-color: #2980b9;
                    color: white;
                    font-weight: bold;
                    border: none;
                    border-radius: 4px;
                    padding: 6px 12px;
                    font-size: 13px;
                }
                QPushButton:hover { background-color: #21618c; }
                QPushButton:pressed { background-color: #1b4f72; }
            """)

        self.btn_boolean_ts_surface.setMaximumHeight(40)  # 从35增大到40
        self.btn_boolean_ts_surface.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                font-weight: bold;
                border: none;
                border-radius: 4px;
                padding: 10px;
                font-size: 14px;
            }
            QPushButton:hover { background-color: #1e8449; }
            QPushButton:pressed { background-color: #186a3b; }
        """)

        # 连接信号
        self.btn_open_ts_model_for_surface_op.clicked.connect(self.open_ts_model_for_surface_op_file)
        self.btn_open_ts_surface.clicked.connect(self.open_ts_surface_file)
        self.btn_select_ts_surface_output.clicked.connect(self.open_output_folder)
        self.btn_boolean_ts_surface.clicked.connect(self.perform_ts_surface_boolean_operations)
        # 连接厚度参数变化信号
        self.thickness_input.textChanged.connect(self.update_thickness_status)

        # 布局
        ts_surface_layout.addWidget(self.btn_open_ts_model_for_surface_op, 0, 0)
        ts_surface_layout.addWidget(self.btn_open_ts_surface, 0, 1)
        ts_surface_layout.addWidget(self.btn_select_ts_surface_output, 0, 2)
        # 添加厚度参数到布局
        ts_surface_layout.addWidget(self.thickness_label, 1, 0, alignment=Qt.AlignRight)
        ts_surface_layout.addWidget(self.thickness_input, 1, 1)
        ts_surface_layout.addWidget(self.btn_boolean_ts_surface, 2, 0, 1, 3)

        ts_surface_group.setLayout(ts_surface_layout)
        advanced_layout.addWidget(ts_surface_group)

        # 添加弹性空间
        advanced_layout.addStretch()
        tab_widget.addTab(advanced_tab, "高级运算")

        # === 选项卡3: 状态信息 ===
        info_tab = QWidget()
        info_layout = QVBoxLayout(info_tab)
        info_layout.setSpacing(10)  # 增大间距
        info_layout.setContentsMargins(15, 15, 15, 15)  # 增大边距

        # 文件信息组 - 蓝色系，增大字体
        file_info_group = QGroupBox("📋 文件状态")
        file_info_group.setStyleSheet("QGroupBox { font-weight: bold; color: #2980b9; font-size: 14px; }")
        file_info_layout = QVBoxLayout()
        file_info_layout.setSpacing(5)  # 增大间距

        # 创建状态标签 - 增大字体
        self.tsurf_label = QLabel("模型文件: 未选择")
        self.dxf_label = QLabel("边界文件: 未选择")
        self.output_label = QLabel("输出文件夹: 未选择")
        self.boundary_label = QLabel("边界体: 未创建")
        self.ts1_label = QLabel("TS模型1: 未选择")
        self.ts2_label = QLabel("TS模型2: 未选择")
        self.ts_model_for_surface_op_label = QLabel("面运算模型: 未选择")
        self.ts_surface_label = QLabel("TS面文件: 未选择")
        self.ts_closure_status_label = QLabel("闭合文件: 未选择")
        self.thickness_status_label = QLabel("挤出厚度: 1")
        self.status_label = QLabel("处理状态: 就绪")

        # 设置标签样式 - 蓝色系，增大字体
        info_labels = [
            self.tsurf_label, self.dxf_label, self.output_label, self.boundary_label,
            self.ts1_label, self.ts2_label, self.ts_model_for_surface_op_label,
            self.ts_surface_label, self.ts_closure_status_label, self.thickness_status_label, self.status_label
        ]
        
        for label in info_labels:
            label.setStyleSheet("""
                QLabel {
                    color: #1b4f72;
                    font-size: 13px;
                    padding: 5px 10px;
                    background-color: #ebf3fd;
                    border: 1px solid #2980b9;
                    border-radius: 3px;
                    margin: 2px;
                    font-weight: bold;
                }
            """)
            file_info_layout.addWidget(label)

        file_info_group.setLayout(file_info_layout)
        info_layout.addWidget(file_info_group)

        # 运行日志组 - 绿色系，增大字体
        log_group = QGroupBox("📝 运行日志")
        log_group.setStyleSheet("QGroupBox { font-weight: bold; color: #27ae60; font-size: 14px; }")
        log_layout = QVBoxLayout()
        log_layout.setSpacing(8)  # 增大间距

        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        self.log_text.setMaximumHeight(180)  # 从150增大到180
        self.log_text.setStyleSheet("""
            QTextEdit {
                background-color: #eafaf1;
                color: #186a3b;
                border: 2px solid #27ae60;
                border-radius: 4px;
                font-family: 'Consolas', 'Courier New', monospace;
                font-size: 12px;
                padding: 8px;
                font-weight: bold;
            }
        """)

        log_layout.addWidget(self.log_text)
        log_group.setLayout(log_layout)
        info_layout.addWidget(log_group)

        info_layout.addStretch()
        tab_widget.addTab(info_tab, "状态信息")

        # === 选项卡4: TS模型闭合 ===
        closure_tab = QWidget()
        closure_layout = QVBoxLayout(closure_tab)
        closure_layout.setSpacing(10)
        closure_layout.setContentsMargins(15, 15, 15, 15)

        # TS模型闭合组 - 橙色系
        closure_group = QGroupBox("🔧 TS模型四面体化闭合")
        closure_group.setStyleSheet("QGroupBox { font-weight: bold; color: #d68910; font-size: 14px; }")
        closure_group_layout = QVBoxLayout()
        closure_group_layout.setSpacing(8)

        # 文件选择区域
        file_select_layout = QGridLayout()
        file_select_layout.setSpacing(8)

        self.btn_select_ts_for_closure = QPushButton("选择TS模型文件")
        self.btn_select_closure_output = QPushButton("选择输出文件夹")

        # 设置按钮样式 - 橙色系
        closure_input_buttons = [self.btn_select_ts_for_closure, self.btn_select_closure_output]
        for btn in closure_input_buttons:
            btn.setMaximumHeight(35)
            btn.setStyleSheet("""
                QPushButton {
                    background-color: #d68910;
                    color: white;
                    font-weight: bold;
                    border: none;
                    border-radius: 4px;
                    padding: 6px 12px;
                    font-size: 13px;
                }
                QPushButton:hover { background-color: #b7950b; }
                QPushButton:pressed { background-color: #9a7d0a; }
            """)

        # 连接信号
        self.btn_select_ts_for_closure.clicked.connect(self.select_ts_for_closure)
        self.btn_select_closure_output.clicked.connect(self.open_output_folder)

        file_select_layout.addWidget(self.btn_select_ts_for_closure, 0, 0)
        file_select_layout.addWidget(self.btn_select_closure_output, 0, 1)

        closure_group_layout.addLayout(file_select_layout)

        # 参数设置区域
        params_closure_layout = QGridLayout()
        params_closure_layout.setSpacing(8)

        # 四面体化参数
        self.quality_label = QLabel("质量参数:")
        self.quality_input = QLineEdit("1.2")
        self.subdivisions_label = QLabel("细分次数:")
        self.subdivisions_input = QLineEdit("0")
        self.repair_holes_label = QLabel("修复孔洞:")
        self.repair_holes_checkbox = QCheckBox()
        self.repair_holes_checkbox.setChecked(True)

        # 设置参数控件样式
        for input_widget in [self.quality_input, self.subdivisions_input]:
            input_widget.setMaximumHeight(30)
            input_widget.setMaximumWidth(100)
            input_widget.setStyleSheet("""
                QLineEdit {
                    padding: 5px;
                    border: 2px solid #d68910;
                    border-radius: 3px;
                    font-size: 13px;
                    background-color: #fef9e7;
                    color: #7d6608;
                }
                QLineEdit:focus {
                    border-color: #f1c40f;
                    background-color: #fdeaa7;
                }
            """)

        for label in [self.quality_label, self.subdivisions_label, self.repair_holes_label]:
            label.setStyleSheet("""
                QLabel {
                    color: #7d6608;
                    font-weight: bold;
                    font-size: 13px;
                }
            """)

        # 布局参数控件
        params_closure_layout.addWidget(self.quality_label, 0, 0, alignment=Qt.AlignRight)
        params_closure_layout.addWidget(self.quality_input, 0, 1)
        params_closure_layout.addWidget(self.subdivisions_label, 0, 2, alignment=Qt.AlignRight)
        params_closure_layout.addWidget(self.subdivisions_input, 0, 3)
        params_closure_layout.addWidget(self.repair_holes_label, 1, 0, alignment=Qt.AlignRight)
        params_closure_layout.addWidget(self.repair_holes_checkbox, 1, 1)

        # 设置列的拉伸因子
        params_closure_layout.setColumnStretch(4, 1)

        closure_group_layout.addLayout(params_closure_layout)

        # 执行按钮
        self.btn_execute_closure = QPushButton("🚀 执行四面体化闭合")
        self.btn_execute_closure.setMaximumHeight(45)
        self.btn_execute_closure.setStyleSheet("""
            QPushButton {
                background-color: #d68910;
                color: white;
                font-weight: bold;
                border: none;
                border-radius: 6px;
                padding: 12px;
                font-size: 15px;
            }
            QPushButton:hover { background-color: #b7950b; }
            QPushButton:pressed { background-color: #9a7d0a; }
        """)
        self.btn_execute_closure.clicked.connect(self.execute_ts_closure)

        closure_group_layout.addWidget(self.btn_execute_closure)

        # 添加工具提示
        self.btn_select_ts_for_closure.setToolTip("选择需要进行四面体化闭合处理的TS模型文件")
        self.btn_select_closure_output.setToolTip("选择处理结果的输出文件夹")
        self.quality_input.setToolTip("四面体网格质量参数，数值越大质量越高但计算时间越长（推荐1.0-2.0）")
        self.subdivisions_input.setToolTip("网格细分次数，用于增加网格密度（0=不细分，1-2=适度细分）")
        self.repair_holes_checkbox.setToolTip("是否在四面体化前先修复网格中的孔洞")
        self.btn_execute_closure.setToolTip("执行完整的四面体化闭合流程，确保TS模型完全水密")

        # 进度条
        self.closure_progress_bar = QProgressBar()
        self.closure_progress_bar.setRange(0, 100)
        self.closure_progress_bar.setValue(0)
        self.closure_progress_bar.setMaximumHeight(25)
        self.closure_progress_bar.setStyleSheet("""
            QProgressBar {
                border: 2px solid #d68910;
                border-radius: 3px;
                text-align: center;
                font-size: 13px;
                background-color: #fef9e7;
                color: #7d6608;
            }
            QProgressBar::chunk {
                background-color: #d68910;
                border-radius: 2px;
            }
        """)

        closure_group_layout.addWidget(self.closure_progress_bar)

        closure_group.setLayout(closure_group_layout)
        closure_layout.addWidget(closure_group)

        # 状态信息组
        closure_status_group = QGroupBox("📋 闭合状态")
        closure_status_group.setStyleSheet("QGroupBox { font-weight: bold; color: #d68910; font-size: 14px; }")
        closure_status_layout = QVBoxLayout()
        closure_status_layout.setSpacing(5)

        self.ts_closure_file_label = QLabel("TS文件: 未选择")
        self.closure_output_label = QLabel("输出文件夹: 未选择")
        self.closure_status_label = QLabel("闭合状态: 就绪")

        closure_status_labels = [self.ts_closure_file_label, self.closure_output_label, self.closure_status_label]
        for label in closure_status_labels:
            label.setStyleSheet("""
                QLabel {
                    color: #7d6608;
                    font-size: 13px;
                    padding: 5px 10px;
                    background-color: #fef9e7;
                    border: 1px solid #d68910;
                    border-radius: 3px;
                    margin: 2px;
                    font-weight: bold;
                }
            """)
            closure_status_layout.addWidget(label)

        closure_status_group.setLayout(closure_status_layout)
        closure_layout.addWidget(closure_status_group)

        closure_layout.addStretch()
        tab_widget.addTab(closure_tab, "TS模型闭合")

        # 将选项卡添加到主布局
        main_layout.addWidget(tab_widget)

        # 添加状态栏
        self.statusBar = QStatusBar()
        self.setStatusBar(self.statusBar)
        self.statusBar.showMessage("就绪")
        self.statusBar.setStyleSheet("""
            QStatusBar {
                background-color: #ebf3fd;
                color: #1b4f72;
                border-top: 2px solid #2980b9;
                font-size: 13px;
                font-weight: bold;
            }
        """)

        # 设置全局样式
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f8f9fa;
            }
            QGroupBox {
                border: 2px solid #bdc3c7;
                border-radius: 6px;
                margin-top: 10px;
                padding-top: 12px;
                background-color: white;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                subcontrol-position: top left;
                padding: 0 10px;
                background-color: white;
                border-radius: 3px;
            }
        """)

        self.show()

    def get_parameters(self):
        """从UI获取参数设置"""
        try:
            height = float(self.height_input.text())
        except ValueError:
            height = 3000.0  # 默认值
            self.height_input.setText(str(height))

        try:
            max_fix_iterations = int(self.fix_iterations_input.text())
        except ValueError:
            max_fix_iterations = 5  # 默认值
            self.fix_iterations_input.setText(str(max_fix_iterations))

        fix_watertight = self.watertight_checkbox.isChecked()

        return {
            "height": height,
            "fix_watertight": fix_watertight,
            "max_fix_iterations": max_fix_iterations,
        }

    @safe_execute(show_error=True)
    def open_tsurf_file(self, checked=False):
        """打开TS文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择TS文件", "", "TS Files (*.ts);;All Files (*)"
        )
        if file_path:
            self.tsurf_file = file_path
            self.tsurf_label.setText(f"TS文件: {os.path.basename(file_path)}")
            self.statusBar.showMessage(f"已加载TS文件: {file_path}")
            self.add_log_message(f"已加载TS文件: {os.path.basename(file_path)}")

    @safe_execute(show_error=True)
    def open_dxf_file(self, checked=False):
        """打开DXF文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择DXF文件", "", "DXF Files (*.dxf);;All Files (*)"
        )
        if file_path:
            self.dxf_file = file_path
            self.dxf_label.setText(f"DXF文件: {os.path.basename(file_path)}")
            self.statusBar.showMessage(f"已加载DXF文件: {file_path}")
            self.add_log_message(f"已加载DXF文件: {os.path.basename(file_path)}")
    
    @safe_execute(show_error=True)
    def open_ts1_file(self, checked=False):
        """打开第一个TS文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择第一个TS模型文件", "", "TS Files (*.ts *.tsurf);;All Files (*)"
        )
        if file_path:
            self.tsurf_file = file_path  # 复用原有的tsurf_file变量
            self.ts1_label.setText(f"TS模型1: {os.path.basename(file_path)}")
            self.statusBar.showMessage(f"已加载TS模型1: {file_path}")
            self.add_log_message(f"已加载TS模型1: {os.path.basename(file_path)}")
            
    @safe_execute(show_error=True)
    def open_ts2_file(self, checked=False):
        """打开第二个TS文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择第二个TS模型文件", "", "TS Files (*.ts *.tsurf);;All Files (*)"
        )
        if file_path:
            self.tsurf_file2 = file_path
            self.ts2_label.setText(f"TS模型2: {os.path.basename(file_path)}")
            self.statusBar.showMessage(f"已加载TS模型2: {file_path}")
            self.add_log_message(f"已加载TS模型2: {os.path.basename(file_path)}")
    
    def open_ts_model_for_surface_op_file(self, checked=False):
        """打开用于与ts面运算的TS模型文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择TS模型文件", "", "TS Files (*.ts *.tsurf);;All Files (*)"
        )
        if file_path:
            self.ts_model_for_surface_op_file = file_path
            self.ts_model_for_surface_op_label.setText(f"用于与面运算的TS模型: {os.path.basename(file_path)}")
            self.statusBar.showMessage(f"已加载用于与面运算的TS模型: {file_path}")

    def open_ts_surface_file(self, checked=False):
        """打开TS面文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择TS面文件", "", "TS Files (*.ts *.tsurf);;All Files (*)"
        )
        if file_path:
            self.ts_surface_file = file_path
            self.ts_surface_label.setText(f"TS面文件: {os.path.basename(file_path)}")
            self.statusBar.showMessage(f"已加载TS面文件: {file_path}")
    
    def update_thickness_status(self, text=None):
        """更新厚度参数状态显示"""
        try:
            thickness = float(self.thickness_input.text())
            if thickness == 0:
                self.thickness_status_label.setText(f"挤出厚度: {thickness} (无效-不能为0)")
            elif thickness > 0:
                self.thickness_status_label.setText(f"挤出厚度: {thickness} (向上)")
            else:  # thickness < 0
                self.thickness_status_label.setText(f"挤出厚度: {thickness} (向下)")
        except ValueError:
            self.thickness_status_label.setText(f"挤出厚度: {self.thickness_input.text()} (无效)")
    
    def select_ts_for_closure(self, checked=False):
        """选择用于闭合的TS文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择TS模型文件", "", "TS Files (*.ts *.tsurf);;All Files (*)"
        )
        if file_path:
            self.ts_closure_file = file_path
            self.ts_closure_file_label.setText(f"TS文件: {os.path.basename(file_path)}")
            self.ts_closure_status_label.setText(f"闭合文件: {os.path.basename(file_path)}")
            self.statusBar.showMessage(f"已加载TS闭合文件: {file_path}")
    
    @safe_execute(show_error=True)
    def execute_ts_closure(self, checked=False):
        """执行TS模型四面体化闭合"""
        # 检查输入
        if not self.ts_closure_file:
            QMessageBox.warning(self, "错误", "请先选择TS模型文件")
            return
            
        # 确保输出文件夹存在
        try:
            self.ensure_output_folder()
        except ValueError as e:
            QMessageBox.warning(self, "错误", str(e))
            return
            
        try:
            self.add_log_message("开始执行TS模型四面体化闭合...")
            self.closure_progress_bar.setValue(0)
            self.closure_status_label.setText("闭合状态: 正在处理...")
            
            # 获取参数
            try:
                quality_param = float(self.quality_input.text())
            except ValueError:
                quality_param = 1.2
                self.quality_input.setText("1.2")
                self.add_log_message("质量参数无效，已重置为默认值: 1.2")
                
            try:
                subdivisions = int(self.subdivisions_input.text())
            except ValueError:
                subdivisions = 0
                self.subdivisions_input.setText("0")
                self.add_log_message("细分次数无效，已重置为默认值: 0")
                
            repair_holes = self.repair_holes_checkbox.isChecked()
            
            self.add_log_message(f"参数设置: 质量={quality_param}, 细分={subdivisions}, 修复孔洞={repair_holes}")
            
            # 1. 读取TS文件数据 (10%)
            self.statusBar.showMessage("步骤 1/6: 读取TS文件...")
            self.closure_progress_bar.setValue(10)
            
            vertices, faces = read_tsurf_data(self.ts_closure_file)
            if len(vertices) == 0 or len(faces) == 0:
                raise ValueError(f"无法从TS文件读取有效数据: {self.ts_closure_file}")
                
            self.add_log_message(f"已读取TS文件: 顶点={len(vertices)}, 面={len(faces)}")
            
            # 2. 转换为PyVista网格 (20%)
            self.statusBar.showMessage("步骤 2/6: 转换为网格...")
            self.closure_progress_bar.setValue(20)
            
            # 创建面数组，PyVista格式需要在每个面前加上点数
            faces_with_header = []
            for face in faces:
                faces_with_header.extend([3, face[0], face[1], face[2]])
            faces_array = np.array(faces_with_header)
            
            # 创建PyVista网格
            mesh = pv.PolyData(vertices, faces_array)
            self.add_log_message(f"已创建PyVista网格: {mesh.n_points}点, {mesh.n_cells}面")
            
            # 3. 预处理网格 (30%)
            self.statusBar.showMessage("步骤 3/6: 预处理网格...")
            self.closure_progress_bar.setValue(30)
            
            # 确保是三角形网格
            mesh = mesh.triangulate()
            
            # 清理网格
            mesh = mesh.clean()
            
            # 统一法向
            mesh.compute_normals(inplace=True)
            
            self.add_log_message(f"网格预处理完成: {mesh.n_points}点, {mesh.n_cells}面")
            
            # 4. 使用pymeshlab进行孔洞修复（如果需要）
            if repair_holes:
                self.statusBar.showMessage("步骤 4a/6: 修复孔洞...")
                self.closure_progress_bar.setValue(40)
                
                try:
                    import pymeshlab
                    
                    # 保存为临时OBJ文件
                    temp_obj = os.path.join(self.output_folder, "temp_for_repair.obj")
                    mesh.save(temp_obj)
                    
                    # 使用pymeshlab修复
                    ms = pymeshlab.MeshSet()
                    ms.load_new_mesh(temp_obj)
                    
                    # 基础清理
                    ms.meshing_remove_duplicate_vertices()
                    ms.meshing_remove_duplicate_faces()
                    
                    # 修复非流形
                    ms.meshing_repair_non_manifold_edges()
                    ms.meshing_repair_non_manifold_vertices()
                    
                    # 闭合孔洞
                    ms.meshing_close_holes(maxholesize=50)
                    
                    # 统一法向
                    ms.meshing_re_orient_faces_coherently()
                    
                    # 保存修复后的网格
                    repaired_obj = os.path.join(self.output_folder, "temp_repaired.obj")
                    ms.save_current_mesh(repaired_obj)
                    
                    # 重新加载为PyVista网格
                    mesh = pv.read(repaired_obj)
                    
                    # 清理临时文件
                    if os.path.exists(temp_obj):
                        os.remove(temp_obj)
                    if os.path.exists(repaired_obj):
                        os.remove(repaired_obj)
                        
                    self.add_log_message(f"孔洞修复完成: {mesh.n_points}点, {mesh.n_cells}面")
                    
                except ImportError:
                    self.add_log_message("警告: 未安装pymeshlab，跳过孔洞修复")
                except Exception as e:
                    self.add_log_message(f"孔洞修复失败: {e}，继续后续处理")
            
            # 5. 使用tetgen进行四面体化 (50%)
            self.statusBar.showMessage("步骤 4/6: 四面体化处理...")
            self.closure_progress_bar.setValue(50)
            
            # 确保网格满足tetgen要求
            if not mesh.is_all_triangles():
                mesh = mesh.triangulate()
                
            self.add_log_message("开始tetgen四面体化处理...")
            
            # 创建tetgen对象
            tet = tetgen.TetGen(mesh)
            
            # 修复流形
            self.add_log_message("修复网格流形结构...")
            tet.make_manifold(verbose=True)
            
            # 细分处理（如果需要）
            if subdivisions > 0:
                self.add_log_message(f"执行 {subdivisions} 次细分...")
                subdivided_mesh = tet.mesh.copy()
                for i in range(subdivisions):
                    try:
                        old_n_cells = subdivided_mesh.n_cells
                        subdivided_mesh = subdivided_mesh.subdivide(nsub=1)
                        self.add_log_message(f"细分 {i+1}: 单元数 {old_n_cells} -> {subdivided_mesh.n_cells}")
                    except Exception as e:
                        self.add_log_message(f"细分 {i+1} 失败: {e}")
                        break
                # 用细分后的网格创建新的tetgen对象
                tet = tetgen.TetGen(subdivided_mesh)
            
            # 生成四面体网格
            self.add_log_message(f"生成四面体网格，质量参数: {quality_param}")
            tet.tetrahedralize(psc=1, quality=quality_param, verbose=1)
            tetrahedral_mesh = tet.grid
            
            self.add_log_message(f"四面体化完成: {tetrahedral_mesh.n_points}点, {tetrahedral_mesh.n_cells}单元")
            
            # 6. 提取表面网格 (70%)
            self.statusBar.showMessage("步骤 5/6: 提取表面网格...")
            self.closure_progress_bar.setValue(70)
            
            surface_mesh = tetrahedral_mesh.extract_surface()
            surface_mesh = surface_mesh.triangulate()
            
            # 统一法向
            surface_mesh.compute_normals(inplace=True)
            
            self.add_log_message(f"表面提取完成: {surface_mesh.n_points}点, {surface_mesh.n_cells}面")
            
            # 7. 保存结果 (90%)
            self.statusBar.showMessage("步骤 6/6: 保存结果...")
            self.closure_progress_bar.setValue(90)
            
            # 生成输出文件名
            input_name = os.path.splitext(os.path.basename(self.ts_closure_file))[0]
            output_ts_path = os.path.join(self.output_folder, f"{input_name}_closed.ts")
            output_vtk_path = os.path.join(self.output_folder, f"{input_name}_closed.vtk")
            output_obj_path = os.path.join(self.output_folder, f"{input_name}_closed.obj")
            
            # 保存为VTK格式
            surface_mesh.save(output_vtk_path)
            self.add_log_message(f"已保存VTK文件: {os.path.basename(output_vtk_path)}")
            
            # 保存为OBJ格式
            surface_mesh.save(output_obj_path)
            self.add_log_message(f"已保存OBJ文件: {os.path.basename(output_obj_path)}")
            
            # 提取顶点和面数据
            final_vertices = np.array(surface_mesh.points)
            final_faces = []
            
            # 提取三角形面
            i = 0
            while i < len(surface_mesh.faces):
                n_points = surface_mesh.faces[i]
                if n_points == 3:
                    final_faces.append([
                        surface_mesh.faces[i + 1],
                        surface_mesh.faces[i + 2],
                        surface_mesh.faces[i + 3]
                    ])
                i += n_points + 1
            
            final_faces = np.array(final_faces)
            
            # 保存为TS格式
            success = self.write_ts_file(
                output_ts_path, 
                final_vertices, 
                final_faces, 
                name=f"{input_name}_closed"
            )
            
            if success:
                self.add_log_message(f"已保存TS文件: {os.path.basename(output_ts_path)}")
            else:
                raise ValueError("保存TS文件失败")
            
            # 完成 (100%)
            self.closure_progress_bar.setValue(100)
            self.closure_status_label.setText("闭合状态: 完成")
            self.statusBar.showMessage("TS模型四面体化闭合完成")
            
            # 显示统计信息
            stats_msg = (
                f"TS模型四面体化闭合完成！\n\n"
                f"原始模型: {len(vertices)}顶点, {len(faces)}面\n"
                f"闭合后模型: {len(final_vertices)}顶点, {len(final_faces)}面\n\n"
                f"输出文件:\n"
                f"• {os.path.basename(output_ts_path)}\n"
                f"• {os.path.basename(output_vtk_path)}\n"
                f"• {os.path.basename(output_obj_path)}\n\n"
                f"保存位置: {self.output_folder}"
            )
            
            self.add_log_message("TS模型四面体化闭合流程全部完成")
            QMessageBox.information(self, "完成", stats_msg)
            
        except Exception as e:
            self.closure_progress_bar.setValue(0)
            self.closure_status_label.setText(f"闭合状态: 失败 - {str(e)}")
            self.statusBar.showMessage("TS模型闭合失败")
            
            error_msg = f"执行TS模型闭合时发生错误:\n{str(e)}"
            self.add_log_message(f"错误: {error_msg}")
            
            import traceback
            self.add_log_message(traceback.format_exc())
            
            QMessageBox.critical(self, "错误", error_msg)
    
    @safe_execute(default_return=False, show_error=True)
    def perform_ts_boolean_operations(self, checked=False):
        """对两个TS模型执行布尔运算 - 基于已经工作的布尔运算方法"""
        # 确保输出文件夹存在
        self.ensure_output_folder()
        
        # 检查是否选择了两个TS文件
        if not self.tsurf_file or not self.tsurf_file2:
            QMessageBox.warning(self, "错误", "请先选择两个TS模型文件")
            return
            
        # 尝试导入pymeshlab
        try:
            import pymeshlab
        except ImportError:
            self.add_log_message("错误: 未安装pymeshlab库，请先安装: pip install pymeshlab")
            QMessageBox.critical(self, "错误", "未安装pymeshlab库，请先安装: pip install pymeshlab")
            return
        
        try:
            self.add_log_message("开始对两个TS模型执行布尔运算...")
            
            # 将TS文件转换为OBJ格式
            self.statusBar.showMessage("正在转换TS文件到OBJ格式...")
            
            # 转换第一个TS文件
            ts1_vertices, ts1_faces = read_tsurf_data(self.tsurf_file)
            if len(ts1_vertices) == 0 or len(ts1_faces) == 0:
                raise ValueError(f"无法从第一个TS文件读取有效数据: {self.tsurf_file}")
            
            ts1_obj_path = os.path.join(self.output_folder, "ts_model1.obj")
            if not self.write_obj_file(ts1_obj_path, ts1_vertices, ts1_faces):
                raise ValueError("无法保存第一个TS模型为OBJ格式")
            self.add_log_message(f"已转换TS模型1为OBJ格式: {os.path.basename(ts1_obj_path)}")
            
            # 转换第二个TS文件
            ts2_vertices, ts2_faces = read_tsurf_data(self.tsurf_file2)
            if len(ts2_vertices) == 0 or len(ts2_faces) == 0:
                raise ValueError(f"无法从第二个TS文件读取有效数据: {self.tsurf_file2}")
            
            ts2_obj_path = os.path.join(self.output_folder, "ts_model2.obj")
            if not self.write_obj_file(ts2_obj_path, ts2_vertices, ts2_faces):
                raise ValueError("无法保存第二个TS模型为OBJ格式")
            self.add_log_message(f"已转换TS模型2为OBJ格式: {os.path.basename(ts2_obj_path)}")
            
            # 设置输出文件路径
            intersection_obj = os.path.join(self.output_folder, "ts_intersection.obj")
            intersection_ts = os.path.join(self.output_folder, "ts_intersection.ts")
            
            difference1_obj = os.path.join(self.output_folder, "ts_difference_1minus2.obj")
            difference1_ts = os.path.join(self.output_folder, "ts_difference_1minus2.ts")
            
            difference2_obj = os.path.join(self.output_folder, "ts_difference_2minus1.obj")
            difference2_ts = os.path.join(self.output_folder, "ts_difference_2minus1.ts")
            
            union_obj = os.path.join(self.output_folder, "ts_union.obj")
            union_ts = os.path.join(self.output_folder, "ts_union.ts")
            
            # 【新增】坐标标准化处理
            self.statusBar.showMessage("正在进行坐标标准化...")
            self.add_log_message("=== 坐标标准化处理 ===")
            
            # 使用PyVista加载网格进行坐标标准化
            mesh1_pv = pv.read(ts1_obj_path)
            mesh2_pv = pv.read(ts2_obj_path)
            
            # 检查坐标范围
            bounds1 = mesh1_pv.bounds
            bounds2 = mesh2_pv.bounds
            max_coord = max(abs(bounds1[0]), abs(bounds1[1]), abs(bounds2[0]), abs(bounds2[1]))
            
            if max_coord > 1000000:  # 如果坐标值大于100万，进行标准化
                self.add_log_message(f"检测到大坐标值 (最大: {max_coord:.0f})，执行坐标标准化...")
                
                # 进行坐标标准化
                mesh1_norm, mesh2_norm, coord_offset = normalize_mesh_coordinates(mesh1_pv, mesh2_pv, verbose=True)
                
                # 保存标准化后的网格
                ts1_norm_path = os.path.join(self.output_folder, "ts_model1_normalized.obj")
                ts2_norm_path = os.path.join(self.output_folder, "ts_model2_normalized.obj")
                
                mesh1_norm.save(ts1_norm_path)
                mesh2_norm.save(ts2_norm_path)
                
                self.add_log_message(f"已保存标准化网格: {os.path.basename(ts1_norm_path)}, {os.path.basename(ts2_norm_path)}")
                self.add_log_message(f"坐标偏移量: X={coord_offset[0]:.2f}, Y={coord_offset[1]:.2f}, Z={coord_offset[2]:.2f}")
                
                # 使用标准化后的文件进行布尔运算
                ts1_for_boolean = ts1_norm_path
                ts2_for_boolean = ts2_norm_path
                coordinate_normalized = True
                
            else:
                self.add_log_message(f"坐标值合理 (最大: {max_coord:.0f})，无需标准化")
                ts1_for_boolean = ts1_obj_path
                ts2_for_boolean = ts2_obj_path
                coordinate_normalized = False
                coord_offset = np.array([0, 0, 0])

            # 1. 执行交集运算 - 使用坐标标准化后的文件
            self.add_log_message("1. 执行交集运算 (采空区)")
            ms = pymeshlab.MeshSet()
            ms.load_new_mesh(ts1_for_boolean)  # TS模型1文件 (可能是标准化的)
            ms.load_new_mesh(ts2_for_boolean)  # TS模型2文件 (可能是标准化的)

            # 记录输入网格的信息
            m1 = ms.current_mesh()
            m1_info = f"TS模型1: {os.path.basename(self.tsurf_file)}, 顶点: {len(m1.vertex_matrix())}, 面: {len(m1.face_matrix())}"
            self.add_log_message(m1_info)

            # 切换到第二个网格并记录信息
            ms.set_current_mesh(1)
            m2 = ms.current_mesh()
            m2_info = f"TS模型2: {os.path.basename(self.tsurf_file2)}, 顶点: {len(m2.vertex_matrix())}, 面: {len(m2.face_matrix())}"
            self.add_log_message(m2_info)

            # 执行布尔交集运算
            ms.set_current_mesh(0)  # 确保选择了第一个网格
            ms.generate_boolean_intersection(first_mesh=0, second_mesh=1)

            # 删除第二个网格，只保留结果
            ms.set_current_mesh(1)
            ms.delete_current_mesh()

            # 记录结果网格的信息
            result_mesh = ms.current_mesh()
            result_info = f"交集结果: 顶点: {len(result_mesh.vertex_matrix())}, 面: {len(result_mesh.face_matrix())}"
            self.add_log_message(result_info)

            # 保存交集结果 - 使用新的坐标还原辅助函数
            save_boolean_result_with_coordinate_restoration(self, ms, intersection_obj, intersection_ts, "交集", coordinate_normalized, coord_offset)

            # 2. 执行差集运算 (模型1 - 模型2)
            self.add_log_message("2. 执行差集运算 (模型1 - 模型2)")
            ms_diff1 = pymeshlab.MeshSet()
            ms_diff1.load_new_mesh(ts1_for_boolean)  # TS模型1文件 (可能是标准化的)
            ms_diff1.load_new_mesh(ts2_for_boolean)  # TS模型2文件 (可能是标准化的)

            # 执行布尔差集运算 (模型1 - 模型2)
            ms_diff1.set_current_mesh(0)  # 确保选择了第一个网格(模型1)
            ms_diff1.generate_boolean_difference(first_mesh=0, second_mesh=1)

            # 删除第二个网格，只保留结果
            ms_diff1.set_current_mesh(1)
            ms_diff1.delete_current_mesh()

            # 记录结果网格的信息
            diff1_mesh = ms_diff1.current_mesh()
            diff1_info = f"差集结果(模型1-模型2): 顶点: {len(diff1_mesh.vertex_matrix())}, 面: {len(diff1_mesh.face_matrix())}"
            self.add_log_message(diff1_info)

            # 保存差集结果 - 使用新的坐标还原辅助函数
            save_boolean_result_with_coordinate_restoration(self, ms_diff1, difference1_obj, difference1_ts, "差集(模型1-模型2)", coordinate_normalized, coord_offset)

            # 3. 执行差集运算 (模型2 - 模型1)
            self.add_log_message("3. 执行差集运算 (模型2 - 模型1)")
            ms_diff2 = pymeshlab.MeshSet()
            ms_diff2.load_new_mesh(ts2_for_boolean)  # TS模型2文件 (可能是标准化的)
            ms_diff2.load_new_mesh(ts1_for_boolean)  # TS模型1文件 (可能是标准化的)

            # 执行布尔差集运算 (模型2 - 模型1)
            ms_diff2.set_current_mesh(0)  # 确保选择了第一个网格(模型2)
            ms_diff2.generate_boolean_difference(first_mesh=0, second_mesh=1)

            # 删除第二个网格，只保留结果
            ms_diff2.set_current_mesh(1)
            ms_diff2.delete_current_mesh()

            # 记录结果网格的信息
            diff2_mesh = ms_diff2.current_mesh()
            diff2_info = f"差集结果(模型2-模型1): 顶点: {len(diff2_mesh.vertex_matrix())}, 面: {len(diff2_mesh.face_matrix())}"
            self.add_log_message(diff2_info)

            # 保存差集结果 - 使用新的坐标还原辅助函数
            save_boolean_result_with_coordinate_restoration(self, ms_diff2, difference2_obj, difference2_ts, "差集(模型2-模型1)", coordinate_normalized, coord_offset)

            # 4. 执行并集运算 (边界与矿体组合)
            self.add_log_message("4. 执行并集运算 (边界与矿体组合)")
            ms_union = pymeshlab.MeshSet()
            ms_union.load_new_mesh(ts1_for_boolean)  # TS模型1文件 (可能是标准化的)
            ms_union.load_new_mesh(ts2_for_boolean)  # TS模型2文件 (可能是标准化的)

            # 执行布尔并集运算
            ms_union.set_current_mesh(0)  # 确保选择了第一个网格
            ms_union.generate_boolean_union(first_mesh=0, second_mesh=1)

            # 删除第二个网格，只保留结果
            ms_union.set_current_mesh(1)
            ms_union.delete_current_mesh()

            # 记录结果网格的信息
            union_mesh = ms_union.current_mesh()
            union_info = f"并集结果: 顶点: {len(union_mesh.vertex_matrix())}, 面: {len(union_mesh.face_matrix())}"
            self.add_log_message(union_info)

            # 保存并集结果 - 使用新的坐标还原辅助函数
            save_boolean_result_with_coordinate_restoration(self, ms_union, union_obj, union_ts, "并集", coordinate_normalized, coord_offset)

            # 显示整体完成信息
            self.add_log_message("TS模型布尔运算完成，生成了四种结果：交集、两种差集和并集")
            
            # 添加完成提示窗口
            QMessageBox.information(
                self,
                "布尔运算完成",
                f"TS模型布尔运算已成功完成！\n\n"
                f"生成了四种结果：\n"
                f"1. 交集: {os.path.basename(intersection_obj)}\n"
                f"2. 差集 (模型1-模型2): {os.path.basename(difference1_obj)}\n"
                f"3. 差集 (模型2-模型1): {os.path.basename(difference2_obj)}\n"
                f"4. 并集: {os.path.basename(union_obj)}\n\n"
                f"所有结果已保存至:\n{self.output_folder}"
            )
            
            self.statusBar.showMessage("TS模型布尔运算完成")
            return True

        except Exception as e:
            self.add_log_message(f"执行TS模型布尔运算时发生错误: {str(e)}")
            import traceback
            self.add_log_message(traceback.format_exc())
            
            # 程序不会因错误而退出，只显示错误信息
            try:
                error_msg = f"TS模型布尔运算过程中出现错误:\n{str(e)}\n\n程序将继续运行，您可以查看日志了解详细信息。"
                QMessageBox.warning(self, "警告", error_msg)
            except:
                self.add_log_message("无法显示警告对话框，但程序继续运行")
                
            self.statusBar.showMessage("TS模型布尔运算失败，但程序继续运行")
            self.add_log_message("⚠️ TS模型布尔运算发生错误，但程序将继续运行以便您查看日志")
            return False

    @safe_execute(default_return=None, show_error=True)
    def create_boundary_body(self, checked=False):
        """创建边界体 - 整合自00_test01.py，支持多个多段线"""
        if not self.dxf_file:
            raise ValueError("请先选择DXF文件")

        # 确保输出文件夹存在
        self.ensure_output_folder()
        
        # 读取DXF边界
        self.statusBar.showMessage("读取DXF边界...")
        boundary_polylines = read_dxf_boundary(self.dxf_file)

        if len(boundary_polylines) == 0:
            raise ValueError("DXF文件中未找到有效边界")

        print(f"找到 {len(boundary_polylines)} 个多段线边界")

        # 为每个多段线创建边界体
        all_boundary_bodies = []
        all_tetrahedral_bodies = []  # 新增：保存每个采空区的四面体化结果

        try:
            top_z = float(self.height_input.text())
        except ValueError:
            top_z = 2000.0 # Default if input is invalid
            self.height_input.setText(str(top_z))
            self.add_log_message(f"顶面Z坐标输入无效，已重置为默认值: {top_z}")
        try:
            bottom_z = float(self.bottom_z_input.text())
        except ValueError:
            bottom_z = -500.0 # Default if input is invalid
            self.bottom_z_input.setText(str(bottom_z))
            self.add_log_message(f"底面Z坐标输入无效，已重置为默认值: {bottom_z}")

        if bottom_z >= top_z:
            QMessageBox.warning(self, "参数错误", "底面Z坐标必须小于顶面Z坐标。将使用默认值。")
            bottom_z = -500.0
            top_z = 2000.0
            self.bottom_z_input.setText(str(bottom_z))
            self.height_input.setText(str(top_z))
            self.add_log_message(f"Z坐标参数无效，已重置为: 底面Z={bottom_z}, 顶面Z={top_z}")
            # Consider returning or raising an error here if processing should stop
            # For now, it proceeds with defaults

        extrusion_height = top_z - bottom_z
        self.add_log_message(f"目标Z范围: [{bottom_z:.2f}, {top_z:.2f}], 拉伸高度: {extrusion_height:.2f}")

        for i, points in enumerate(boundary_polylines):
            print(f"处理多段线 {i+1}/{len(boundary_polylines)}...")

            # 计算每个多段线的中心点（用于调试输出）
            center = np.mean(points, axis=0)
            print(
                f"多段线 {i+1} 中心点: ({center[0]:.2f}, {center[1]:.2f}, {center[2]:.2f})"
            )

            # 为当前多边形创建输出子目录
            polyline_dir = os.path.join(self.output_folder, f"polyline_{i+1}")
            os.makedirs(polyline_dir, exist_ok=True)

            # Adjust Z of the input points for the current polyline
            current_polyline_points_at_bottom = points.copy()
            current_polyline_points_at_bottom[:, 2] = bottom_z

            # 使用boundary_test.py中的方法创建边界体
            boundary_body = create_boundary_from_closed_polyline(
                current_polyline_points_at_bottom, # Use points shifted to bottom_z
                extrusion_height,                  # Use calculated extrusion_height
                output_dir=polyline_dir,
                verbose=True,
            )
            all_boundary_bodies.append(boundary_body)

            # 保存每个独立的边界体到输出文件夹
            out_path = os.path.join(self.output_folder, f"boundary_body_{i+1}.vtk")
            boundary_body.save(out_path)
            print(
                f"已创建边界体 {i+1}: 点数={boundary_body.n_points}, 面数={boundary_body.n_cells}"
            )

            # 统一边界体的法向量，确保面片朝外
            try:
                print(f"统一采空区 {i+1} 的法向量...")
                # 尝试标准方法
                boundary_body_unified = unify_normals(boundary_body, outward=True)
                # 直接将统一法向后的边界体添加到列表中
                all_tetrahedral_bodies.append(boundary_body_unified)
                print(f"已跳过采空区 {i+1} 的四面体化处理，直接使用统一法向后的边界体")
                self.add_log_message(f"已跳过采空区 {i+1} 的四面体化处理，使用统一法向后的边界体")
                
                # 保存并修复每个独立边界体的法向
                individual_obj_path = os.path.join(self.output_folder, f"boundary_{i+1}.obj")
                self.convert_mesh_to_obj(boundary_body_unified, individual_obj_path)
                # 修复该边界体的法向 - 对所有边界体使用增强的通用处理方法
                fix_boundary_obj_normals(individual_obj_path, is_single_component=True)
                # 重新加载修复后的边界体
                individual_fixed_vtk = os.path.join(self.output_folder, f"boundary_{i+1}_fixed.vtk")
                # 使用pymeshlab读取obj并保存为vtk
                try:
                    import pymeshlab
                    ms = pymeshlab.MeshSet()
                    ms.load_new_mesh(individual_obj_path)
                    
                    # 对每个边界体进行自动法向检测和修复 - 不依赖于特定序号
                    print(f"对boundary_{i+1}.obj执行智能法向处理...")
                    # 检查主要法向方向
                    detect_and_fix_normals(ms)
                    
                    ms.save_current_mesh(individual_fixed_vtk)
                    # 加载修复后的vtk
                    boundary_body_unified = pv.read(individual_fixed_vtk)
                    # 更新列表中的边界体
                    all_tetrahedral_bodies[-1] = boundary_body_unified
                    print(f"已修复并重新加载采空区 {i+1} 的边界体")
                except Exception as e:
                    print(f"修复并重新加载采空区 {i+1} 边界体时出错: {e}")
                
            except Exception as e:
                print(f"统一法向失败: {e}")
                print("尝试备用方法...")
                # 备选方案: 如果unify_normals失败，直接使用原始边界体
                all_tetrahedral_bodies.append(boundary_body)
                print(f"已跳过采空区 {i+1} 的法向统一，直接使用原始边界体")
                self.add_log_message(f"警告: 采空区 {i+1} 法向统一失败，使用原始边界体")

        # 合并所有边界体
        if len(all_boundary_bodies) > 1:
            print("合并多个边界体...")
            boundary_body = all_boundary_bodies[0]

            for i, body in enumerate(all_boundary_bodies[1:], 1):
                try:
                    # 逐个合并，方便追踪问题
                    old_point_count = boundary_body.n_points
                    old_face_count = boundary_body.n_cells

                    boundary_body = boundary_body.merge(body)

                    print(
                        f"合并边界体 {i+1}: 合并前={old_point_count}点/{old_face_count}面, "
                        f"合并后={boundary_body.n_points}点/{boundary_body.n_cells}面"
                    )

                except Exception as e:
                    print(f"合并边界体 {i+1} 时出错: {e}")
        else:
            boundary_body = all_boundary_bodies[0]

        print(
            f"最终边界体: 点数={boundary_body.n_points}, 面数={boundary_body.n_cells}"
        )

        # 保存合并后的完整边界体到输出文件夹
        boundary_full_path = os.path.join(self.output_folder, self.boundary_body_path)
        boundary_body.save(boundary_full_path)
        self.boundary_mesh = boundary_body  # 保存到实例变量以供后续使用

        # 同样的方法合并四面体化后的边界体
        if len(all_tetrahedral_bodies) > 1:
            print("合并四面体化后的边界体...")
            tetrahedral_body = all_tetrahedral_bodies[0]
            for i, body in enumerate(all_tetrahedral_bodies[1:], 1):
                try:
                    tetrahedral_body = tetrahedral_body.merge(body)
                    print(
                        f"合并四面体化边界体 {i+1}: 点数={tetrahedral_body.n_points}, 面数={tetrahedral_body.n_cells}"
                    )
                except Exception as e:
                    print(f"合并四面体化边界体 {i+1} 时出错: {e}")
        else:
            tetrahedral_body = all_tetrahedral_bodies[0]

        # 提取表面网格并保存
        # 使用三角化处理确保模型是干净的三角形网格
        tetrahedral_surface = tetrahedral_body.extract_surface()
        tetrahedral_trisurf = tetrahedral_surface.triangulate()
        
        # 再次统一整个模型的法向，确保所有部分的法向一致朝外
        print("统一最终模型的法向...")
        tetrahedral_trisurf_unified = unify_normals(tetrahedral_trisurf, outward=True)
        
        # 保存为VTK文件
        refined_path = os.path.join(self.output_folder, "refined_boundary.vtk")
        tetrahedral_surface.save(refined_path)
        
        refined_tri_path = os.path.join(
            self.output_folder, "refined_boundary_tetrahedral.vtk"
        )
        tetrahedral_trisurf_unified.save(refined_tri_path)

        # 保存为OBJ文件以便后续处理
        obj_path = os.path.join(self.output_folder, "refined_boundary_tetrahedral.obj")
        self.convert_mesh_to_obj(tetrahedral_trisurf_unified, obj_path)

        # 保存表面网格用于检查
        refined_surface_path = os.path.join(
            self.output_folder, "refined_boundary_surface.vtk"
        )
        tetrahedral_surface.save(refined_surface_path)

        # 新增：直接导出非归一化的边界体OBJ格式并确保法向统一
        try:
            print("\n=== 导出非归一化边界体和矿体 ===")
            original_boundary_obj_path = os.path.join(
                self.output_folder, "boundary_original.obj"
            )

            # 导出边界体OBJ格式
            self.convert_mesh_to_obj(tetrahedral_trisurf_unified, original_boundary_obj_path)
            print(f"已保存非归一化边界体OBJ: {original_boundary_obj_path}")
            self.add_log_message(
                f"已导出边界体: {os.path.basename(original_boundary_obj_path)}"
            )
            
            # 修复boundary_original.obj的法向 - 使用增强的泛化处理方法
            if fix_boundary_obj_normals(original_boundary_obj_path, is_single_component=False):
                self.add_log_message(f"已统一边界体法向: {os.path.basename(original_boundary_obj_path)}")
                
                # 修复后进行二次检查，确保所有法向正确
                try:
                    import pymeshlab
                    ms = pymeshlab.MeshSet()
                    ms.load_new_mesh(original_boundary_obj_path)
                    
                    # 使用智能检测和修复函数
                    print("对boundary_original.obj执行二次检查...")
                    detect_and_fix_normals(ms)
                    
                    # 保存修复后的模型
                    ms.save_current_mesh(original_boundary_obj_path)
                    self.add_log_message(f"已二次检查并修复边界体法向")
                    
                    # 始终创建一个备用版本，以备不时之需
                    try:
                        backup_obj_path = os.path.join(self.output_folder, "boundary_original_alt.obj")
                        ms = pymeshlab.MeshSet()
                        ms.load_new_mesh(original_boundary_obj_path)
                        # 创建一个备用版本（法向方向相反）
                        ms.meshing_invert_face_orientation()
                        ms.save_current_mesh(backup_obj_path)
                        self.add_log_message(f"已创建备用边界体(法向相反): boundary_original_alt.obj")
                    except Exception as e:
                        print(f"创建备用边界体时出错(可忽略): {e}")
                except Exception as e:
                    print(f"二次检查法向时出错(可忽略): {e}")
            else:
                self.add_log_message(f"警告: 统一边界体法向失败")

            # 导出矿体OBJ格式（从加载的TS文件转换）
            if self.tsurf_file and os.path.exists(self.tsurf_file):
                original_obj_path = os.path.join(self.output_folder, "original.obj")
                # 读取TS文件并转换为OBJ
                ts_vertices, ts_faces = read_tsurf_data(self.tsurf_file)
                if len(ts_vertices) > 0 and len(ts_faces) > 0:
                    self.write_obj_file(original_obj_path, ts_vertices, ts_faces)
                    print(f"已保存矿体OBJ: {original_obj_path}")
                    self.add_log_message(
                        f"已导出矿体: {os.path.basename(original_obj_path)}"
                    )
                else:
                    print(f"无法从TS文件读取有效数据: {self.tsurf_file}")
                    self.add_log_message(
                        f"警告: 无法从TS文件读取有效数据，未生成original.obj"
                    )
            else:
                print(f"警告: 未选择TS文件或文件不存在，未生成original.obj")
                self.add_log_message(
                    f"警告: 未选择TS文件或文件不存在，未生成original.obj"
                )
        except Exception as e:
            print(f"导出非归一化OBJ文件时出错: {e}")
            self.add_log_message(f"错误: 导出非归一化OBJ文件失败 - {str(e)}")

        # 更新UI
        self.boundary_label.setText(
            f"边界体: 已创建 ({boundary_body.n_points}点/{boundary_body.n_cells}面)"
        )
        self.statusBar.showMessage("边界体创建完成")
        
        # 添加警告计数日志
        if self.warning_count > 0:
            warning_message = f"处理过程中共出现 {self.warning_count} 次警告"
            if self.warning_count >= 20:
                warning_message += "，已跳过四面体化处理"
            print(warning_message)
            self.add_log_message(warning_message)

        # 清理临时的 polyline_{i+1} 文件夹
        if self.output_folder and os.path.exists(self.output_folder):
            try:
                import shutil # 导入shutil
                for item in os.listdir(self.output_folder):
                    item_path = os.path.join(self.output_folder, item)
                    if os.path.isdir(item_path) and item.startswith("polyline_"):
                        shutil.rmtree(item_path)
                        self.add_log_message(f"已删除临时文件夹: {item_path}")
            except Exception as e:
                self.add_log_message(f"清理临时文件夹时出错: {e}")

        return boundary_body

    # 新增辅助方法，将PyVista网格转换为OBJ格式
    def convert_mesh_to_obj(self, mesh, obj_path):
        """将PyVista网格转换为OBJ格式"""
        print(f"将网格转换为OBJ: {obj_path}")
        try:
            # 确保网格是三角形网格
            tri_mesh = mesh.triangulate()
            vertices = np.array(tri_mesh.points)
            faces = []

            # 提取三角形面
            if hasattr(tri_mesh, "faces"):
                i = 0
                while i < len(tri_mesh.faces):
                    n_points = tri_mesh.faces[i]
                    if n_points == 3:
                        faces.append(
                            [
                                tri_mesh.faces[i + 1],
                                tri_mesh.faces[i + 2],
                                tri_mesh.faces[i + 3],
                            ]
                        )
                    i += n_points + 1

            # 如果无法直接获取，尝试通过cells_dict获取
            elif hasattr(tri_mesh, "cells_dict") and "triangle" in tri_mesh.cells_dict:
                faces = tri_mesh.cells_dict["triangle"]

            # 保存OBJ文件
            if vertices.size > 0 and len(faces) > 0:
                with open(obj_path, "w") as f:
                    for v in vertices:
                        f.write(f"v {v[0]} {v[1]} {v[2]}\n")
                    for face in faces:
                        f.write(f"f {face[0]+1} {face[1]+1} {face[2]+1}\n")
                print(f"成功写入OBJ文件: {obj_path}")
                return True
            else:
                print(f"错误: 无法从网格提取有效的顶点或面")
                return False
        except Exception as e:
            print(f"转换网格到OBJ失败: {e}")
            return False

    @safe_execute(show_error=True)
    def run_all_process(self, checked=False):
        """一键执行完整处理流程"""
        if not self.tsurf_file or not self.dxf_file:
            QMessageBox.warning(self, "错误", "请先选择TS文件和DXF文件")
            return

        # 询问用户选择处理模式
        msg_box = QMessageBox(self)
        msg_box.setWindowTitle("选择处理模式")
        msg_box.setText("请选择处理模式:\n单边界处理 - 适用于简单边界\n多工作面处理 - 适用于复杂多边界")
        btn_single = msg_box.addButton("单边界处理", QMessageBox.AcceptRole) # YesRole or AcceptRole
        btn_multi = msg_box.addButton("多工作面处理", QMessageBox.RejectRole) # NoRole or RejectRole
        msg_box.setDefaultButton(btn_single)
        msg_box.exec_()

        reply = msg_box.clickedButton()

        use_tetra_method = (reply == btn_multi) # True if "多工作面处理" is clicked

        try:
            # 设置为一键处理模式（不生成PNG）
            self.is_step_by_step = False

            # 存储实例引用以便visualize_and_save函数可以访问处理模式
            IntegratedProcessor.instance = self

            # 获取参数
            params = self.get_parameters()

            # 确保输出文件夹存在
            self.ensure_output_folder()

            # 重置进度条并启用
            self.progress_bar.setValue(0)

            # 更新状态
            mode_text = "多工作面" if use_tetra_method else "单边界"
            self.statusBar.showMessage(f"正在执行完整处理流程 ({mode_text}模式)...")
            self.status_label.setText(f"处理状态: 开始完整处理 ({mode_text}模式)...")

            # 1. 创建边界体 - 50%
            self.statusBar.showMessage(f"步骤 1/2: 创建边界体 ({mode_text}模式)...")
            self.progress_bar.setValue(0)
            
            if use_tetra_method:
                self.create_boundary_body_tetra()
            else:
                self.create_boundary_body()
                
            self.progress_bar.setValue(50)

            # 2. 执行布尔运算 - 100%
            self.statusBar.showMessage("步骤 2/2: 执行布尔运算...")
            boolean_success = self.perform_boolean_operations_for_all_process()
            if not boolean_success:
                raise RuntimeError("布尔运算失败：请检查边界文件和矿体文件是否正确生成")
            self.progress_bar.setValue(100)

            # 恢复分步处理模式
            self.is_step_by_step = True

            # 更新状态 - 100%
            self.statusBar.showMessage("处理完成")
            self.status_label.setText(f"处理状态: 完整处理流程完成 ({mode_text}模式)")

            QMessageBox.information(
                self,
                "成功",
                f"完整处理流程 ({mode_text}模式) 已成功执行!\n结果保存在: {self.output_folder}",
            )

        except Exception as e:
            # 恢复分步处理模式
            self.is_step_by_step = True

            # 进度条停在失败位置
            QMessageBox.critical(self, "错误", f"执行处理流程时出错: {str(e)}")
            self.statusBar.showMessage("处理失败")
            self.status_label.setText(f"处理状态: 失败 - {str(e)}")

    def ensure_output_folder(self):
        """确保输出文件夹存在，并返回路径"""
        if not self.output_folder:
            QMessageBox.warning(self, "错误", "请先选择输出文件夹")
            raise ValueError("未选择输出文件夹")
        if not os.path.exists(self.output_folder):
            os.makedirs(self.output_folder)
        return self.output_folder

    def read_obj_file(self, file_path):
        """读取OBJ文件并返回顶点和面信息"""
        # 尝试不同的编码
        encodings_to_try = ["utf-8", "latin-1", "cp1252", "gbk", "gb2312"]

        for encoding in encodings_to_try:
            try:
                vertices = []
                faces = []

                with open(file_path, "r", encoding=encoding) as file:
                    for line in file:
                        if line.startswith("v "):  # 顶点
                            parts = line.strip().split()
                            if len(parts) >= 4:  # v x y z
                                vertex = [
                                    float(parts[1]),
                                    float(parts[2]),
                                    float(parts[3]),
                                ]
                                vertices.append(vertex)
                        elif line.startswith("f "):  # 面
                            parts = line.strip().split()
                            if len(parts) >= 4:  # f v1 v2 v3
                                # OBJ索引从1开始，需要减1
                                face = []
                                for part in parts[1:]:
                                    # 处理形如"1/2/3"的索引，只取第一个数字
                                    vertex_idx = int(part.split("/")[0]) - 1
                                    face.append(vertex_idx)
                                if len(face) == 3:  # 只处理三角形面
                                    faces.append(face)

                if len(vertices) > 0 and len(faces) > 0:
                    print(f"成功使用 {encoding} 编码读取OBJ文件: {file_path}")
                    return np.array(vertices), np.array(faces)

            except UnicodeDecodeError:
                continue
            except Exception as e:
                print(f"使用 {encoding} 读取OBJ文件时出错: {e}")
                continue

        # 如果所有编码尝试都失败，尝试二进制模式
        try:
            print("尝试使用二进制模式读取OBJ文件...")
            vertices = []
            faces = []

            with open(file_path, "rb") as file:
                for line in file:
                    try:
                        # 尝试用latin-1解码行（它可以解码任何字节）
                        decoded_line = line.decode("latin-1")
                        if decoded_line.startswith("v "):  # 顶点
                            parts = decoded_line.strip().split()
                            if len(parts) >= 4:  # v x y z
                                try:
                                    vertex = [
                                        float(parts[1]),
                                        float(parts[2]),
                                        float(parts[3]),
                                    ]
                                    vertices.append(vertex)
                                except ValueError:
                                    continue
                        elif decoded_line.startswith("f "):  # 面
                            parts = decoded_line.strip().split()
                            if len(parts) >= 4:  # f v1 v2 v3
                                try:
                                    # OBJ索引从1开始，需要减1
                                    face = []
                                    for part in parts[1:]:
                                        # 处理形如"1/2/3"的索引，只取第一个数字
                                        vertex_idx = int(part.split("/")[0]) - 1
                                        face.append(vertex_idx)
                                    if len(face) == 3:  # 只处理三角形面
                                        faces.append(face)
                                except ValueError:
                                    continue
                    except Exception as e:
                        continue

            if len(vertices) > 0 and len(faces) > 0:
                print(
                    f"成功使用二进制模式读取OBJ文件，获取了 {len(vertices)} 个顶点和 {len(faces)} 个面"
                )
                return np.array(vertices), np.array(faces)

        except Exception as e:
            print(f"二进制模式读取OBJ文件失败: {e}")

        # 如果所有方法都失败，返回空数组
        print(f"无法读取OBJ文件 {file_path}，尝试了所有编码方式")
        return np.array([]), np.array([])

    def write_ts_file(self, file_path, vertices, faces, name=None):
        """将顶点和面信息写入TS文件"""
        try:
            with open(file_path, "w") as file:
                file.write(f"{len(vertices)} {len(faces)}\n")
                for vertex in vertices:
                    file.write(f"{vertex[0]} {vertex[1]} {vertex[2]}\n")
                for face in faces:
                    file.write(f"{face[0]} {face[1]} {face[2]}\n")
                if name:
                    file.write(f"{name}\n")
            print(f"成功写入TS文件: {file_path}")
            return True
        except Exception as e:
            print(f"写入TS文件失败: {e}")
            return False

    def write_obj_file(self, file_path, vertices, faces):
        """将顶点和面信息写入OBJ文件"""
        try:
            with open(file_path, "w") as file:
                for vertex in vertices:
                    file.write(f"v {vertex[0]} {vertex[1]} {vertex[2]}\n")
                for face in faces:
                    file.write(f"f {face[0]+1} {face[1]+1} {face[2]+1}\n")
            print(f"成功写入OBJ文件: {file_path}")
            return True
        except Exception as e:
            print(f"写入OBJ文件失败: {e}")
            return False




# 删除重复的方法定义

    def perform_boolean_operations_for_all_process(self):
        """执行布尔运算 - 供一键处理使用，使用预生成的文件"""
        # 确保输出文件夹存在
        self.ensure_output_folder()

        # 检查必要的输入文件
        if not self.tsurf_file:
            raise ValueError("请先加载TS文件")

        # 设置布尔运算的自动流程标志
        self.add_log_message("开始执行自动布尔计算流程")

        # 尝试导入pymeshlab
        try:
            import pymeshlab
        except ImportError:
            self.add_log_message(
                "错误: 未安装pymeshlab库，请先安装: pip install pymeshlab"
            )
            raise ValueError("未安装pymeshlab库，请先安装: pip install pymeshlab")

        # 步骤1: 查找输入文件
        self.statusBar.showMessage("1/2: 查找输入文件...")
        try:
            # 找到边界文件 - 优先使用非归一化边界体
            boundary_file = os.path.join(self.output_folder, "boundary_original.obj")
            if not os.path.exists(boundary_file):
                error_msg = f"边界文件不存在: {boundary_file}"
                self.add_log_message(f"错误: {error_msg}")
                raise FileNotFoundError(error_msg)

            # 找到矿体文件 - 使用非归一化原始文件
            ore_file = os.path.join(self.output_folder, "original.obj")
            if not os.path.exists(ore_file):
                error_msg = f"矿体文件不存在: {ore_file}"
                self.add_log_message(f"错误: {error_msg}")
                raise FileNotFoundError(error_msg)

            self.add_log_message(
                f"找到输入文件: 边界文件={os.path.basename(boundary_file)}, 矿体文件={os.path.basename(ore_file)}"
            )

            # 步骤2: 执行布尔运算
            self.statusBar.showMessage("2/2: 执行布尔运算...")

            # 设置输出文件路径
            output_obj = os.path.join(self.output_folder, "boolean_result.obj")  # 保持原名
            output_ts = os.path.join(self.output_folder, "boolean_result.ts")    # 保持原名
            
            difference_obj = os.path.join(self.output_folder, "boolean_difference.obj")  # 剩余矿体
            difference_ts = os.path.join(self.output_folder, "boolean_difference.ts")
            
            union_obj = os.path.join(self.output_folder, "boolean_union.obj")  # 边界与矿体组合
            union_ts = os.path.join(self.output_folder, "boolean_union.ts")

            # 1. 执行交集运算 (采空区)
            self.add_log_message("1. 执行交集运算 (采空区)")
            ms = pymeshlab.MeshSet()
            ms.load_new_mesh(boundary_file)  # 边界体文件
            ms.load_new_mesh(ore_file)  # 矿体文件

            # 记录输入网格的信息
            m1 = ms.current_mesh()
            m1_info = f"边界体: {os.path.basename(boundary_file)}, 顶点: {len(m1.vertex_matrix())}, 面: {len(m1.face_matrix())}"
            self.add_log_message(m1_info)

            # 切换到第二个网格并记录信息
            ms.set_current_mesh(1)
            m2 = ms.current_mesh()
            m2_info = f"矿体: {os.path.basename(ore_file)}, 顶点: {len(m2.vertex_matrix())}, 面: {len(m2.face_matrix())}"
            self.add_log_message(m2_info)

            # 执行布尔交集运算
            ms.set_current_mesh(0)  # 确保选择了第一个网格
            ms.generate_boolean_intersection(first_mesh=0, second_mesh=1)

            # 删除第二个网格，只保留结果
            ms.set_current_mesh(1)
            ms.delete_current_mesh()

            # 记录结果网格的信息
            result_mesh = ms.current_mesh()
            result_info = f"交集结果: 顶点: {len(result_mesh.vertex_matrix())}, 面: {len(result_mesh.face_matrix())}"
            self.add_log_message(result_info)

            # 保存结果为OBJ格式
            ms.save_current_mesh(output_obj)
            self.add_log_message(f"已保存布尔运算结果为OBJ格式: {output_obj}")

            # 保存为TS格式 - 使用和perform_boolean_operations相同的方式
            if self.obj_to_ts(output_obj, output_ts, name="boolean_result"):
                self.add_log_message(f"已转换并保存交集结果为TS格式: {output_ts}")
            else:
                self.add_log_message(f"警告: 交集结果转换为TS格式失败")

            # 2. 执行差集运算 (剩余矿体)
            self.add_log_message("2. 执行差集运算 (剩余矿体)")
            ms_diff = pymeshlab.MeshSet()
            ms_diff.load_new_mesh(ore_file)  # 矿体文件
            ms_diff.load_new_mesh(boundary_file)  # 边界体文件

            # 执行布尔差集运算 (矿体 - 边界体 = 剩余矿体)
            ms_diff.set_current_mesh(0)  # 确保选择了第一个网格(矿体)
            ms_diff.generate_boolean_difference(first_mesh=0, second_mesh=1)

            # 删除第二个网格，只保留结果
            ms_diff.set_current_mesh(1)
            ms_diff.delete_current_mesh()

            # 记录结果网格的信息
            diff_mesh = ms_diff.current_mesh()
            diff_info = f"差集结果: 顶点: {len(diff_mesh.vertex_matrix())}, 面: {len(diff_mesh.face_matrix())}"
            self.add_log_message(diff_info)

            # 保存差集结果
            ms_diff.save_current_mesh(difference_obj)
            self.add_log_message(f"已保存差集结果为OBJ格式: {difference_obj}")

            # 使用obj_to_ts转换为TS格式
            if self.obj_to_ts(difference_obj, difference_ts, name="boolean_difference"):
                self.add_log_message(f"已转换并保存差集结果为TS格式: {difference_ts}")
            else:
                self.add_log_message(f"警告: 差集结果转换为TS格式失败")

            # 3. 执行并集运算 (边界与矿体组合)
            self.add_log_message("3. 执行并集运算 (边界与矿体组合)")
            ms_union = pymeshlab.MeshSet()
            ms_union.load_new_mesh(boundary_file)  # 边界体文件
            ms_union.load_new_mesh(ore_file)  # 矿体文件

            # 执行布尔并集运算
            ms_union.set_current_mesh(0)  # 确保选择了第一个网格
            ms_union.generate_boolean_union(first_mesh=0, second_mesh=1)

            # 删除第二个网格，只保留结果
            ms_union.set_current_mesh(1)
            ms_union.delete_current_mesh()

            # 记录结果网格的信息
            union_mesh = ms_union.current_mesh()
            union_info = f"并集结果: 顶点: {len(union_mesh.vertex_matrix())}, 面: {len(union_mesh.face_matrix())}"
            self.add_log_message(union_info)

            # 保存并集结果
            ms_union.save_current_mesh(union_obj)
            self.add_log_message(f"已保存并集结果为OBJ格式: {union_obj}")

            # 使用obj_to_ts转换为TS格式
            if self.obj_to_ts(union_obj, union_ts, name="boolean_union"):
                self.add_log_message(f"已转换并保存并集结果为TS格式: {union_ts}")
            else:
                self.add_log_message(f"警告: 并集结果转换为TS格式失败")

            # 显示整体完成信息
            self.add_log_message("布尔运算完成，生成了三种结果：交集、差集和并集")
            
            # 添加完成提示窗口
            QMessageBox.information(
                self,
                "布尔运算完成",
                f"布尔运算已成功完成！\n\n"
                f"生成了三种结果：\n"
                f"1. 交集（采空区）: {os.path.basename(output_obj)}\n"
                f"2. 差集（剩余矿体）: {os.path.basename(difference_obj)}\n"
                f"3. 并集（边界与矿体组合）: {os.path.basename(union_obj)}\n\n"
                f"所有结果已保存至:\n{self.output_folder}"
            )
            
            self.statusBar.showMessage("布尔运算完成")
            return True

        except Exception as e:
            self.add_log_message(f"执行布尔运算时发生错误: {str(e)}")
            import traceback
            self.add_log_message(traceback.format_exc())
            
            # 程序不会因错误而退出，显示详细错误信息
            try:
                QMessageBox.critical(self, "错误", f"一键处理的布尔运算发生错误:\n{str(e)}\n\n程序将继续运行，您可以查看日志了解详细信息。")
            except:
                self.add_log_message("无法显示错误对话框，但程序继续运行")
            
            self.statusBar.showMessage("一键处理的布尔运算失败，但程序继续运行")
            self.add_log_message("⚠️ 一键处理的布尔运算发生错误，但程序将继续运行以便您查看日志")
            return False

    def obj_to_ts(
        self,
        obj_file,
        ts_file=None,
        name=None,
        fix_watertight=True,
        max_fix_iterations=5,
    ):
        """将OBJ文件转换为TS（GOCAD Tsurf）格式"""
        try:
            # 确定输出TS文件名
            if ts_file is None:
                ts_file = os.path.splitext(obj_file)[0] + ".ts"

            # 确定名称
            if name is None:
                name = os.path.splitext(os.path.basename(obj_file))[0]

            # 读取OBJ文件
            vertices, faces = self.read_obj_file(obj_file)
            if len(vertices) == 0 or len(faces) == 0:
                self.add_log_message(f"警告: {obj_file} 文件中未找到有效数据")
                return False

            # 写入TS文件
            with open(ts_file, "w") as file:
                # 写入TS文件头
                file.write(f"GOCAD TSURF 1\nHEADER {{\nname: {name}\n}}\n")
                file.write("TFACE\n")

                # 写入顶点(TS使用1-based索引)
                for i, v in enumerate(vertices):
                    file.write(f"VRTX {i+1} {v[0]:.6f} {v[1]:.6f} {v[2]:.6f}\n")

                # 写入面(TS索引从1开始)
                for t in faces:
                    file.write(f"TRGL {t[0]+1} {t[1]+1} {t[2]+1}\n")

                # 写入结束标记
                file.write("END\n")

            self.add_log_message(
                f"已将 {os.path.basename(obj_file)} 转换为 {os.path.basename(ts_file)}"
            )
            return True

        except Exception as e:
            self.add_log_message(f"OBJ到TS转换失败: {str(e)}")
            return False

    def flip_normals_and_export_ts(self, checked=False):
        """加载TS或OBJ文件，翻转法线，然后导出为新的文件"""
        try:
            # 打开文件 - 支持TS和OBJ格式
            input_file, _ = QFileDialog.getOpenFileName(
                self, "选择要翻转法线的文件", "", "支持的文件 (*.ts *.tsurf *.obj)"
            )
            if not input_file:
                return

            # 确定文件类型
            file_ext = os.path.splitext(input_file)[1].lower()

            # 读取文件数据
            mesh = None
            if file_ext in [".ts", ".tsurf"]:
                # 读取TS文件数据
                points, triangles = read_tsurf_data(input_file)

                # 转换为Open3D网格
                mesh = o3d.geometry.TriangleMesh()
                mesh.vertices = o3d.utility.Vector3dVector(points)
                mesh.triangles = o3d.utility.Vector3iVector(triangles)
                self.add_log_message(
                    f"已读取TS文件: {os.path.basename(input_file)}, 点数={len(points)}, 面数={len(triangles)}"
                )
            elif file_ext == ".obj":
                # 读取OBJ文件数据
                vertices, faces = self.read_obj_file(input_file)

                # 转换为Open3D网格
                mesh = o3d.geometry.TriangleMesh()
                mesh.vertices = o3d.utility.Vector3dVector(vertices)
                mesh.triangles = o3d.utility.Vector3iVector(faces)
                self.add_log_message(
                    f"已读取OBJ文件: {os.path.basename(input_file)}, 点数={len(vertices)}, 面数={len(faces)}"
                )
            else:
                raise ValueError(f"不支持的文件格式: {file_ext}")

            # 确保读取的数据有效
            if mesh is None or len(mesh.vertices) == 0 or len(mesh.triangles) == 0:
                raise ValueError("无法从文件中读取有效数据")

            # 进行法线翻转
            flipped_mesh = self.flip_mesh(mesh)

            # 设置输出路径 - 保存到原始文件相同路径
            file_dir = os.path.dirname(input_file)
            base_name = os.path.basename(input_file)
            name_without_ext = os.path.splitext(base_name)[0]

            # 提取顶点和三角形
            flipped_vertices = np.asarray(flipped_mesh.vertices)
            flipped_triangles = np.asarray(flipped_mesh.triangles)

            # 根据原始文件类型保存输出
            if file_ext in [".ts", ".tsurf"]:
                # 保存为TS文件
                output_file = os.path.join(file_dir, f"{name_without_ext}_flipped.ts")
                success = self.write_ts_file(
                    output_file,
                    flipped_vertices,
                    flipped_triangles,
                    name=f"{name_without_ext}_flipped",
                )
                self.add_log_message(
                    f"已保存翻转后的TS文件: {os.path.basename(output_file)}"
                )

                # 同时保存为OBJ格式
                obj_output_file = os.path.join(
                    file_dir, f"{name_without_ext}_flipped.obj"
                )
                obj_success = self.write_obj_file(
                    obj_output_file, flipped_vertices, flipped_triangles
                )
                if obj_success:
                    self.add_log_message(
                        f"已保存翻转后的OBJ文件: {os.path.basename(obj_output_file)}"
                    )
            else:
                # 保存为OBJ文件
                output_file = os.path.join(file_dir, f"{name_without_ext}_flipped.obj")
                success = self.write_obj_file(
                    output_file, flipped_vertices, flipped_triangles
                )
                self.add_log_message(
                    f"已保存翻转后的OBJ文件: {os.path.basename(output_file)}"
                )

                # 同时保存为TS格式
                ts_output_file = os.path.join(
                    file_dir, f"{name_without_ext}_flipped.ts"
                )
                ts_success = self.write_ts_file(
                    ts_output_file,
                    flipped_vertices,
                    flipped_triangles,
                    name=f"{name_without_ext}_flipped",
                )
                if ts_success:
                    self.add_log_message(
                        f"已保存翻转后的TS文件: {os.path.basename(ts_output_file)}"
                    )

            if success:
                self.statusBar.showMessage(f"法线翻转成功，已保存至 {output_file}")
                QMessageBox.information(
                    self,
                    "成功",
                    f"法线已成功翻转并保存:\n- {os.path.basename(output_file)}",
                )
            else:
                self.statusBar.showMessage("法线翻转失败")
                QMessageBox.warning(self, "警告", "法线翻转失败")
        except Exception as e:
            import traceback

            error_details = traceback.format_exc()
            self.statusBar.showMessage(f"法线翻转失败: {str(e)}")
            self.add_log_message(f"错误: 法线翻转失败 - {str(e)}")
            QMessageBox.critical(
                self,
                "错误",
                f"法线翻转过程中出错: {str(e)}\n\n详细信息:\n{error_details}",
            )

    # 添加日志处理方法
    def add_log_message(self, message):
        """将消息添加到日志窗口 - 安全版本"""
        try:
            timestamp = datetime.now().strftime("%H:%M:%S")
            formatted_message = f"[{timestamp}] {message}"

            # 如果日志窗口存在，添加消息
            if hasattr(self, 'log_text') and self.log_text is not None:
                self.log_text.append(formatted_message)

                # 安全的滚动到底部
                try:
                    cursor = self.log_text.textCursor()
                    cursor.movePosition(QTextCursor.End)
                    self.log_text.setTextCursor(cursor)
                except:
                    pass  # 滚动失败不影响程序运行

                # 安全的UI刷新
                try:
                    QApplication.processEvents()
                except:
                    pass  # UI刷新失败不影响程序运行
            
            # 无论如何都在控制台输出
            print(formatted_message)
            
        except Exception as e:
            # 如果连日志都失败了，至少在控制台输出
            print(f"[日志错误] 无法记录消息: {message} - 错误: {e}")
            print(f"[{datetime.now().strftime('%H:%M:%S')}] {message}")

    def open_output_folder(self, checked=False):
        """选择输出文件夹"""
        dir_path = QFileDialog.getExistingDirectory(self, "选择输出文件夹", "")
        if dir_path:
            self.output_folder = dir_path
            self.output_label.setText(f"输出文件夹: {os.path.basename(dir_path)}")
            # 同步更新闭合功能的输出标签
            if hasattr(self, 'closure_output_label'):
                self.closure_output_label.setText(f"输出文件夹: {os.path.basename(dir_path)}")
            self.statusBar.showMessage(f"已选择输出文件夹: {dir_path}")

    @safe_execute(default_return=False, show_error=True)
    def perform_boolean_operations(self, checked=False):
        """执行布尔运算，用户选择的两个文件进行布尔运算"""
        # 确保输出文件夹存在
        self.ensure_output_folder()

        # 让用户选择文件，而不是从不存在的输入框获取
        file1, _ = QFileDialog.getOpenFileName(
            self, "选择边界体文件", "", "OBJ Files (*.obj);;All Files (*)"
        )
        if not file1:
            self.add_log_message("错误：未选择边界体文件")
            return

        file2, _ = QFileDialog.getOpenFileName(
            self, "选择矿体文件", "", "OBJ Files (*.obj);;All Files (*)"
        )
        if not file2:
            self.add_log_message("错误：未选择矿体文件")
            return

        if not os.path.exists(file1) or not os.path.exists(file2):
            self.add_log_message(
                f"错误：文件不存在 {file1 if not os.path.exists(file1) else file2}"
            )
            return

        # 尝试导入pymeshlab
        try:
            import pymeshlab
        except ImportError:
            self.add_log_message(
                "错误: 未安装pymeshlab库，请先安装: pip install pymeshlab"
            )
            return

        try:
            self.add_log_message(
                f"开始执行布尔运算: {os.path.basename(file1)} 与 {os.path.basename(file2)}"
            )

            # 设置各种布尔运算结果的输出文件路径
            intersection_obj = os.path.join(self.output_folder, "boolean_result.obj")  # 保持原名
            intersection_ts = os.path.join(self.output_folder, "boolean_result.ts")    # 保持原名
            
            difference_obj = os.path.join(self.output_folder, "boolean_difference.obj")  # 剩余矿体
            difference_ts = os.path.join(self.output_folder, "boolean_difference.ts")
            
            union_obj = os.path.join(self.output_folder, "boolean_union.obj")  # 边界与矿体组合
            union_ts = os.path.join(self.output_folder, "boolean_union.ts")

            # 1. 执行交集运算 (采空区)
            self.add_log_message("1. 执行交集运算 (采空区)")
            ms = pymeshlab.MeshSet()
            ms.load_new_mesh(file1)  # 边界体文件
            ms.load_new_mesh(file2)  # 矿体文件

            # 记录输入网格的信息
            m1 = ms.current_mesh()
            m1_info = f"边界体: {os.path.basename(file1)}, 顶点: {len(m1.vertex_matrix())}, 面: {len(m1.face_matrix())}"
            self.add_log_message(m1_info)

            # 切换到第二个网格并记录信息
            ms.set_current_mesh(1)
            m2 = ms.current_mesh()
            m2_info = f"矿体: {os.path.basename(file2)}, 顶点: {len(m2.vertex_matrix())}, 面: {len(m2.face_matrix())}"
            self.add_log_message(m2_info)

            # 执行布尔交集运算
            ms.set_current_mesh(0)  # 确保选择了第一个网格
            ms.generate_boolean_intersection(first_mesh=0, second_mesh=1)

            # 删除第二个网格，只保留结果
            ms.set_current_mesh(1)
            ms.delete_current_mesh()

            # 记录结果网格的信息
            result_mesh = ms.current_mesh()
            result_info = f"交集结果: 顶点: {len(result_mesh.vertex_matrix())}, 面: {len(result_mesh.face_matrix())}"
            self.add_log_message(result_info)

            # 保存交集结果为OBJ格式
            ms.save_current_mesh(intersection_obj)
            self.add_log_message(f"已保存交集结果为OBJ格式: {intersection_obj}")

            # 使用obj_to_ts转换为TS格式
            if self.obj_to_ts(intersection_obj, intersection_ts, name="boolean_result"):
                self.add_log_message(f"已转换并保存交集结果为TS格式: {intersection_ts}")
            else:
                self.add_log_message(f"警告: 交集结果转换为TS格式失败")

            # 2. 执行差集运算 (剩余矿体)
            self.add_log_message("2. 执行差集运算 (剩余矿体)")
            ms_diff = pymeshlab.MeshSet()
            ms_diff.load_new_mesh(file2)  # 矿体文件
            ms_diff.load_new_mesh(file1)  # 边界体文件

            # 执行布尔差集运算 (矿体 - 边界体 = 剩余矿体)
            ms_diff.set_current_mesh(0)  # 确保选择了第一个网格(矿体)
            ms_diff.generate_boolean_difference(first_mesh=0, second_mesh=1)

            # 删除第二个网格，只保留结果
            ms_diff.set_current_mesh(1)
            ms_diff.delete_current_mesh()

            # 记录结果网格的信息
            diff_mesh = ms_diff.current_mesh()
            diff_info = f"差集结果: 顶点: {len(diff_mesh.vertex_matrix())}, 面: {len(diff_mesh.face_matrix())}"
            self.add_log_message(diff_info)

            # 保存差集结果
            ms_diff.save_current_mesh(difference_obj)
            self.add_log_message(f"已保存差集结果为OBJ格式: {difference_obj}")

            # 使用obj_to_ts转换为TS格式
            if self.obj_to_ts(difference_obj, difference_ts, name="boolean_difference"):
                self.add_log_message(f"已转换并保存差集结果为TS格式: {difference_ts}")
            else:
                self.add_log_message(f"警告: 差集结果转换为TS格式失败")

            # 3. 执行并集运算 (边界与矿体组合)
            self.add_log_message("3. 执行并集运算 (边界与矿体组合)")
            ms_union = pymeshlab.MeshSet()
            ms_union.load_new_mesh(file1)  # 边界体文件
            ms_union.load_new_mesh(file2)  # 矿体文件

            # 执行布尔并集运算
            ms_union.set_current_mesh(0)  # 确保选择了第一个网格
            ms_union.generate_boolean_union(first_mesh=0, second_mesh=1)

            # 删除第二个网格，只保留结果
            ms_union.set_current_mesh(1)
            ms_union.delete_current_mesh()

            # 记录结果网格的信息
            union_mesh = ms_union.current_mesh()
            union_info = f"并集结果: 顶点: {len(union_mesh.vertex_matrix())}, 面: {len(union_mesh.face_matrix())}"
            self.add_log_message(union_info)

            # 保存并集结果
            ms_union.save_current_mesh(union_obj)
            self.add_log_message(f"已保存并集结果为OBJ格式: {union_obj}")

            # 使用obj_to_ts转换为TS格式
            if self.obj_to_ts(union_obj, union_ts, name="boolean_union"):
                self.add_log_message(f"已转换并保存并集结果为TS格式: {union_ts}")
            else:
                self.add_log_message(f"警告: 并集结果转换为TS格式失败")

            # 显示整体完成信息
            self.add_log_message("布尔运算完成，生成了三种结果：交集、差集和并集")
            
            # 添加完成提示窗口
            QMessageBox.information(
                self,
                "布尔运算完成",
                f"布尔运算已成功完成！\n\n"
                f"生成了三种结果：\n"
                f"1. 交集（采空区）: {os.path.basename(intersection_obj)}\n"
                f"2. 差集（剩余矿体）: {os.path.basename(difference_obj)}\n"
                f"3. 并集（边界与矿体组合）: {os.path.basename(union_obj)}\n\n"
                f"所有结果已保存至:\n{self.output_folder}"
            )
            
            self.statusBar.showMessage("布尔运算完成")
            return True

        except Exception as e:
            self.add_log_message(f"执行布尔运算时发生错误: {str(e)}")
            import traceback
            self.add_log_message(traceback.format_exc())
            
            # 程序不会因错误而退出，显示详细错误信息
            try:
                QMessageBox.critical(self, "错误", f"手动布尔运算发生错误:\n{str(e)}\n\n程序将继续运行，您可以查看日志了解详细信息。")
            except:
                self.add_log_message("无法显示错误对话框，但程序继续运行")
            
            self.statusBar.showMessage("手动布尔运算失败，但程序继续运行")
            self.add_log_message("⚠️ 手动布尔运算发生错误，但程序将继续运行以便您查看日志")
            return False

    def flip_mesh(self, mesh):
        """翻转网格的朝向（通过反转三角形顶点顺序）"""
        if mesh is None:
            return None
            
        # 创建副本避免修改原始网格
        flipped_mesh = copy.deepcopy(mesh)
        
        # 获取三角形数组
        triangles = np.asarray(flipped_mesh.triangles)
        
        # 反转每个三角形的顶点顺序
        flipped_triangles = np.zeros_like(triangles)
        for i, triangle in enumerate(triangles):
            flipped_triangles[i] = [triangle[0], triangle[2], triangle[1]]  # 交换第二个和第三个顶点
        
        # 更新网格的三角形
        flipped_mesh.triangles = o3d.utility.Vector3iVector(flipped_triangles)
        
        # 需要重新计算法线
        flipped_mesh.compute_vertex_normals()
        flipped_mesh.compute_triangle_normals()
        
        return flipped_mesh

    @safe_execute(default_return=None, show_error=True)
    def create_boundary_body_tetra(self, checked=False):
        """创建边界体 - 使用四面体处理方法，适合多工作面"""
        if not self.dxf_file:
            raise ValueError("请先选择DXF文件")

        # 确保输出文件夹存在
        self.ensure_output_folder()
        
        # 读取DXF边界
        self.statusBar.showMessage("读取DXF边界...")
        self.add_log_message("使用四面体方法处理多工作面边界...")
        boundary_polylines = read_dxf_boundary(self.dxf_file)

        if len(boundary_polylines) == 0:
            raise ValueError("DXF文件中未找到有效边界")

        print(f"找到 {len(boundary_polylines)} 个多段线边界")
        
        # 为每个多段线创建边界体
        all_boundary_bodies = []
        all_tetrahedral_bodies = []  # 保存每个采空区的四面体化结果

        try:
            top_z = float(self.height_input.text())
        except ValueError:
            top_z = 2000.0 # Default if input is invalid
            self.height_input.setText(str(top_z))
            self.add_log_message(f"顶面Z坐标输入无效，已重置为默认值: {top_z}")
        try:
            bottom_z = float(self.bottom_z_input.text())
        except ValueError:
            bottom_z = -500.0 # Default if input is invalid
            self.bottom_z_input.setText(str(bottom_z))
            self.add_log_message(f"底面Z坐标输入无效，已重置为默认值: {bottom_z}")

        if bottom_z >= top_z:
            QMessageBox.warning(self, "参数错误", "底面Z坐标必须小于顶面Z坐标。将使用默认值。")
            bottom_z = -500.0
            top_z = 2000.0
            self.bottom_z_input.setText(str(bottom_z))
            self.height_input.setText(str(top_z))
            self.add_log_message(f"Z坐标参数无效，已重置为: 底面Z={bottom_z}, 顶面Z={top_z}")
            # Consider returning or raising an error here if processing should stop

        extrusion_height = top_z - bottom_z
        self.add_log_message(f"目标Z范围 (四面体法): [{bottom_z:.2f}, {top_z:.2f}], 拉伸高度: {extrusion_height:.2f}")

        for i, points in enumerate(boundary_polylines):
            print(f"处理多段线 {i+1}/{len(boundary_polylines)}...")

            # 计算每个多段线的中心点（用于调试输出）
            center = np.mean(points, axis=0)
            print(
                f"多段线 {i+1} 中心点: ({center[0]:.2f}, {center[1]:.2f}, {center[2]:.2f})"
            )

            # 为当前多边形创建输出子目录
            polyline_dir = os.path.join(self.output_folder, f"polyline_{i+1}")
            os.makedirs(polyline_dir, exist_ok=True)

            # Adjust Z of the input points for the current polyline
            current_polyline_points_at_bottom = points.copy()
            current_polyline_points_at_bottom[:, 2] = bottom_z

            # 使用boundary_test.py中的方法创建边界体
            boundary_body = create_boundary_from_closed_polyline(
                current_polyline_points_at_bottom, # Use points shifted to bottom_z
                extrusion_height,                  # Use calculated extrusion_height
                output_dir=polyline_dir,
                verbose=True,
            )
            all_boundary_bodies.append(boundary_body)

            # 保存每个独立的边界体到输出文件夹
            out_path = os.path.join(self.output_folder, f"boundary_body_{i+1}.vtk")
            boundary_body.save(out_path)
            print(
                f"已创建边界体 {i+1}: 点数={boundary_body.n_points}, 面数={boundary_body.n_cells}"
            )

            # 为每个采空区单独进行四面体化处理
            try:
                # 确保边界网格是三角形网格
                print(f"将采空区 {i+1} 转换为三角形网格...")
                tri_mesh = boundary_body.triangulate()

                # 修复流形
                print(f"修复采空区 {i+1} 的流形结构...")
                tet = tetgen.TetGen(tri_mesh)
                tet.make_manifold(verbose=True)

                # 注释掉细分步骤，直接使用修复后的网格
                # print(f"细分采空区 {i+1} 以增加密度...")
                # subdivided_mesh = tet.mesh.copy()
                # max_subdivisions = 1  # 减少细分次数以避免过复杂化
                # 
                # for j in range(max_subdivisions):
                #     if subdivided_mesh.n_cells == 0:
                #         print(f"警告: 采空区 {i+1} 没有单元，无法继续细分")
                #         break
                # 
                #     try:
                #         old_n_cells = subdivided_mesh.n_cells
                #         subdivided_mesh = subdivided_mesh.subdivide(nsub=1)
                #         print(
                #             f"采空区 {i+1} 细分 {j+1}: 单元数 {old_n_cells} -> {subdivided_mesh.n_cells}"
                #         )
                #     except Exception as e:
                #         print(f"采空区 {i+1} 细分失败: {e}")
                #         break
                
                # 直接使用修复后的网格，不进行细分
                print(f"跳过细分步骤，提高计算速度...")
                subdivided_mesh = tet.mesh.copy()

                # 生成四面体网格
                print(f"为采空区 {i+1} 生成四面体网格...")
                tet = tetgen.TetGen(subdivided_mesh)
                tet.tetrahedralize(psc=1, quality=1, verbose=1)
                tetrahedral_mesh = tet.grid

                # 提取表面网格
                print(f"提取采空区 {i+1} 的表面网格...")
                surface_mesh = tetrahedral_mesh.extract_surface()

                # 保存这个采空区的四面体化结果
                all_tetrahedral_bodies.append(surface_mesh)
                
                print(f"已完成采空区 {i+1} 的四面体化处理，点数={surface_mesh.n_points}, 面数={surface_mesh.n_cells}")
                
            except Exception as e:
                print(f"采空区 {i+1} 四面体化失败: {e}")
                print(f"使用原始边界体作为备选")
                all_tetrahedral_bodies.append(boundary_body)
                self.add_log_message(f"警告: 采空区 {i+1} 四面体化处理失败，使用原始边界体")

        # 合并所有四面体化后的边界体
        if len(all_tetrahedral_bodies) > 1:
            print("合并多个四面体化边界体...")
            tetrahedral_body = all_tetrahedral_bodies[0]
            for i, body in enumerate(all_tetrahedral_bodies[1:], 1):
                try:
                    tetrahedral_body = tetrahedral_body.merge(body)
                    print(
                        f"合并四面体化边界体 {i+1}: 点数={tetrahedral_body.n_points}, 面数={tetrahedral_body.n_cells}"
                    )
                except Exception as e:
                    print(f"合并四面体化边界体 {i+1} 时出错: {e}")
        else:
            tetrahedral_body = all_tetrahedral_bodies[0]

        # 合并所有原始边界体
        if len(all_boundary_bodies) > 1:
            print("合并多个原始边界体...")
            boundary_body = all_boundary_bodies[0]
            
            for i, body in enumerate(all_boundary_bodies[1:], 1):
                try:
                    # 逐个合并，方便追踪问题
                    old_point_count = boundary_body.n_points
                    old_face_count = boundary_body.n_cells

                    boundary_body = boundary_body.merge(body)

                    print(
                        f"合并边界体 {i+1}: 合并前={old_point_count}点/{old_face_count}面, "
                        f"合并后={boundary_body.n_points}点/{boundary_body.n_cells}面"
                    )
                except Exception as e:
                    print(f"合并原始边界体 {i+1} 时出错: {e}")
        else:
            boundary_body = all_boundary_bodies[0]

        # 保存合并后的四面体化边界体
        tetrahedral_path = os.path.join(self.output_folder, "boundary_tetrahedral.vtk")
        tetrahedral_body.save(tetrahedral_path)
        self.add_log_message(f"已保存四面体化边界体: boundary_tetrahedral.vtk")

        # 保存合并后的原始边界体
        boundary_full_path = os.path.join(self.output_folder, self.boundary_body_path)
        boundary_body.save(boundary_full_path)
        self.boundary_mesh = boundary_body  # 保存到实例变量以供后续使用

        # 提取表面网格
        tetrahedral_surface = tetrahedral_body.extract_surface()
        tetrahedral_trisurf = tetrahedral_surface.triangulate()
        
        # 保存为OBJ文件
        original_boundary_obj_path = os.path.join(
            self.output_folder, "boundary_original.obj"
        )
        self.convert_mesh_to_obj(tetrahedral_trisurf, original_boundary_obj_path)
        print(f"已保存四面体化边界体OBJ: {original_boundary_obj_path}")
        self.add_log_message(f"已导出边界体: {os.path.basename(original_boundary_obj_path)}")

        # 导出矿体OBJ格式（从加载的TS文件转换）
        if self.tsurf_file and os.path.exists(self.tsurf_file):
            original_obj_path = os.path.join(self.output_folder, "original.obj")
            # 读取TS文件并转换为OBJ
            ts_vertices, ts_faces = read_tsurf_data(self.tsurf_file)
            if len(ts_vertices) > 0 and len(ts_faces) > 0:
                self.write_obj_file(original_obj_path, ts_vertices, ts_faces)
                print(f"已保存矿体OBJ: {original_obj_path}")
                self.add_log_message(f"已导出矿体: {os.path.basename(original_obj_path)}")
            else:
                print(f"无法从TS文件读取有效数据: {self.tsurf_file}")
                self.add_log_message(f"警告: 无法从TS文件读取有效数据，未生成original.obj")
        else:
            print(f"警告: 未选择TS文件或文件不存在，未生成original.obj")
            self.add_log_message(f"警告: 未选择TS文件或文件不存在，未生成original.obj")

        # 更新UI
        self.boundary_label.setText(
            f"边界体: 已创建 ({tetrahedral_body.n_points}点/{tetrahedral_body.n_cells}面)"
        )
        self.statusBar.showMessage("四面体边界体创建完成")
        
        # 清理临时的 polyline_{i+1} 文件夹
        if self.output_folder and os.path.exists(self.output_folder):
            try:
                import shutil # 导入shutil
                for item in os.listdir(self.output_folder):
                    item_path = os.path.join(self.output_folder, item)
                    if os.path.isdir(item_path) and item.startswith("polyline_"):
                        shutil.rmtree(item_path)
                        self.add_log_message(f"已删除临时文件夹: {item_path}")
            except Exception as e:
                self.add_log_message(f"清理临时文件夹时出错: {e}")
                
        return tetrahedral_body

    def save_mesh_manually(self, meshset, output_path, mesh_name="manual_save"):
        """手动保存网格为OBJ格式，作为PyMeshLab保存失败的备用方案"""
        try:
            mesh = meshset.current_mesh()
            vertices = mesh.vertex_matrix()
            faces = mesh.face_matrix()
            
            self.add_log_message(f"开始手动保存网格到: {output_path}")
            self.add_log_message(f"网格信息: {len(vertices)}顶点, {len(faces)}面")
            
            # 确保输出目录存在
            os.makedirs(os.path.dirname(output_path), exist_ok=True)
            
            # 手动写入OBJ文件
            with open(output_path, 'w', encoding='utf-8') as f:
                # 写入头部注释
                f.write(f"# OBJ文件 - {mesh_name}\n")
                f.write(f"# 顶点数: {len(vertices)}\n")
                f.write(f"# 面数: {len(faces)}\n\n")
                
                # 写入顶点
                for v in vertices:
                    f.write(f"v {v[0]:.10f} {v[1]:.10f} {v[2]:.10f}\n")
                
                # 写入面 (OBJ格式索引从1开始)
                for face in faces:
                    if len(face) == 3:
                        f.write(f"f {face[0]+1} {face[1]+1} {face[2]+1}\n")
                    elif len(face) == 4:  # 四边形面
                        f.write(f"f {face[0]+1} {face[1]+1} {face[2]+1} {face[3]+1}\n")
            
            self.add_log_message(f"手动保存网格成功: {os.path.basename(output_path)}")
            return True
            
        except Exception as e:
            self.add_log_message(f"手动保存网格失败: {str(e)}")
            return False

    def perform_ts_surface_boolean_operations(self, checked=False):
        """对一个TS模型和一个TS面执行布尔运算"""
        try:
            self.ensure_output_folder()

            if not self.ts_model_for_surface_op_file or not self.ts_surface_file:
                QMessageBox.warning(self, "错误", "请先选择TS模型和TS面文件")
                return
                
            # 使用WRL格式作为中间文件格式，更好地保持网格封闭性
            intermediate_format = "wrl"  # WRL格式更适合保持封闭网格
            self.add_log_message(f"使用 {intermediate_format.upper()} 作为中间文件格式")

            try:
                import pymeshlab
                import pyvista as pv
            except ImportError as e:
                self.add_log_message(f"错误: 缺少必要的库: {e}。请安装 pymeshlab 和 pyvista")
                QMessageBox.critical(self, "错误", f"缺少必要的库: {e}。请安装 pymeshlab 和 pyvista")
                return

            try:
                self.add_log_message("开始执行TS模型与TS面布尔运算...")

                # 1. 读取TS模型并转换为WRL
                self.statusBar.showMessage("正在转换TS模型到WRL...")
                model_vertices, model_faces = read_tsurf_data(self.ts_model_for_surface_op_file)
                if len(model_vertices) == 0:
                    raise ValueError("无法从TS模型文件读取有效数据")
                
                # 先转换为OBJ，再用PyMeshLab转换为WRL以保持质量
                temp_model_obj = os.path.join(self.output_folder, "temp_model.obj")
                model_wrl_path = os.path.join(self.output_folder, "model_for_surface_op.wrl")
                
                if not self.write_obj_file(temp_model_obj, model_vertices, model_faces):
                    raise ValueError("无法保存TS模型为临时OBJ格式")

                # 修复TS模型，确保是封闭的，并转换为WRL
                try:
                    ms_fix_model = pymeshlab.MeshSet()
                    ms_fix_model.load_new_mesh(temp_model_obj)
                    
                    # 修复非流形边缘和顶点
                    ms_fix_model.meshing_remove_duplicate_vertices()
                    ms_fix_model.meshing_remove_duplicate_faces()
                    ms_fix_model.meshing_repair_non_manifold_edges()
                    ms_fix_model.meshing_repair_non_manifold_vertices()
                    
                    # 统一法向
                    ms_fix_model.meshing_re_orient_faces_coherently()
                    ms_fix_model.compute_normal_per_face()
                    ms_fix_model.compute_normal_per_vertex()
                    
                    # 尝试闭合孔洞
                    try:
                        ms_fix_model.meshing_close_holes(maxholesize=30)
                        self.add_log_message("已修复TS模型的封闭性")
                    except:
                        self.add_log_message("TS模型无需修复或修复失败")
                    
                    # 先保存为临时PLY，然后用PyVista转换为WRL
                    temp_ply_path = os.path.join(self.output_folder, "temp_model.ply")
                    ms_fix_model.save_current_mesh(temp_ply_path)
                    
                    # 使用PyVista读取PLY并保存为WRL
                    model_mesh = pv.read(temp_ply_path)
                    plotter = pv.Plotter(off_screen=True)
                    plotter.add_mesh(model_mesh)
                    plotter.export_vrml(model_wrl_path)
                    self.add_log_message(f"已转换TS模型为WRL: {os.path.basename(model_wrl_path)}")
                    
                    # 清理临时文件
                    if os.path.exists(temp_model_obj):
                        os.remove(temp_model_obj)
                    if os.path.exists(temp_ply_path):
                        os.remove(temp_ply_path)
                    
                except Exception as e:
                    self.add_log_message(f"修复TS模型时出现警告: {e}")
                    # 如果失败，直接保存原始数据为WRL
                    if not os.path.exists(model_wrl_path):
                        # 使用PyVista作为备用方案
                        try:
                            faces_with_header = np.hstack([np.full((len(model_faces), 1), 3), model_faces])
                            model_mesh = pv.PolyData(model_vertices, faces_with_header)
                            plotter = pv.Plotter(off_screen=True)
                            plotter.add_mesh(model_mesh)
                            plotter.export_vrml(model_wrl_path)
                            self.add_log_message(f"使用PyVista保存TS模型为WRL: {os.path.basename(model_wrl_path)}")
                        except Exception as e2:
                            raise ValueError(f"无法保存TS模型为WRL格式: {e2}")

                # 【新增】坐标标准化处理
                self.statusBar.showMessage("正在进行坐标标准化...")
                self.add_log_message("=== 坐标标准化处理 ===")
                
                # 使用临时OBJ文件检查模型文件的坐标范围（WRL文件PyVista无法直接读取）
                temp_model_obj_for_check = os.path.join(self.output_folder, "temp_model_for_check.obj")
                if not self.write_obj_file(temp_model_obj_for_check, model_vertices, model_faces):
                    raise ValueError("无法创建临时模型文件用于坐标检查")
                
                model_mesh_pv = pv.read(temp_model_obj_for_check)
                model_bounds = model_mesh_pv.bounds
                max_model_coord = max(abs(model_bounds[0]), abs(model_bounds[1]), abs(model_bounds[2]), abs(model_bounds[3]))
                
                self.add_log_message(f"TS模型坐标范围检查，最大坐标值: {max_model_coord:.0f}")
                
                # 清理临时文件
                if os.path.exists(temp_model_obj_for_check):
                    os.remove(temp_model_obj_for_check)

                # 2. 读取TS面, 并挤出为实体
                self.statusBar.showMessage("正在处理TS面文件...")
                surface_vertices, surface_faces = read_tsurf_data(self.ts_surface_file)
                if len(surface_vertices) == 0:
                    raise ValueError("无法从TS面文件读取有效数据")
                    
                # 先将TS面保存为临时OBJ并使用PyMeshLab处理法向问题
                temp_surface_obj = os.path.join(self.output_folder, "temp_surface.obj")
                self.write_obj_file(temp_surface_obj, surface_vertices, surface_faces)
                
                # 使用PyMeshLab加载并统一法向，然后保存为WRL
                processed_surface_wrl = os.path.join(self.output_folder, "processed_surface.wrl")
                try:
                    ms_surface = pymeshlab.MeshSet()
                    ms_surface.load_new_mesh(temp_surface_obj)
                    
                    # 统一法向
                    ms_surface.meshing_re_orient_faces_coherently()
                    ms_surface.compute_normal_per_face()
                    ms_surface.compute_normal_per_vertex()
                    self.add_log_message("已统一TS面的法向")
                    
                    # 先保存为临时PLY，然后用PyVista转换为WRL
                    temp_surface_ply = os.path.join(self.output_folder, "temp_surface.ply")
                    ms_surface.save_current_mesh(temp_surface_ply)
                    
                    # 使用PyVista读取PLY并保存为WRL
                    surface_mesh_temp = pv.read(temp_surface_ply)
                    plotter = pv.Plotter(off_screen=True)
                    plotter.add_mesh(surface_mesh_temp)
                    plotter.export_vrml(processed_surface_wrl)
                    
                    # 清理临时PLY文件
                    if os.path.exists(temp_surface_ply):
                        os.remove(temp_surface_ply)
                    
                    # 重新加载WRL数据到PyVista进行挤出
                    surface_mesh = pv.read(processed_surface_wrl)
                    
                    # 清理临时文件
                    if os.path.exists(temp_surface_obj):
                        os.remove(temp_surface_obj)
                        
                except Exception as e:
                    self.add_log_message(f"处理TS面法向时出现警告: {e}")
                    # 备用方案：直接使用原始数据
                    faces_with_header = np.hstack([np.full((len(surface_faces), 1), 3), surface_faces])
                    surface_mesh = pv.PolyData(surface_vertices, faces_with_header)

                # 如果没有从WRL加载成功，使用备用方案
                if 'surface_mesh' not in locals():
                    faces_with_header = np.hstack([np.full((len(surface_faces), 1), 3), surface_faces])
                    surface_mesh = pv.PolyData(surface_vertices, faces_with_header)

                # 先清理和修复面网格
                surface_mesh = surface_mesh.clean()
                surface_mesh = surface_mesh.triangulate()
                
                # 统一法向 - 确保所有面的法向方向一致
                try:
                    # 计算并统一法向
                    surface_mesh.compute_normals(cell_normals=True, point_normals=True, 
                                               consistent_normals=True, 
                                               auto_orient_normals=True,
                                               inplace=True)
                    self.add_log_message("已统一TS面的法向方向")
                except Exception as e:
                    self.add_log_message(f"统一法向时出现警告: {e}")

                # 挤出面为一个实体。
                model_mesh = pv.PolyData(model_vertices)
                bounds = model_mesh.bounds
                
                # 从UI获取厚度参数
                try:
                    thickness = float(self.thickness_input.text())
                    if thickness == 0:
                        raise ValueError("厚度不能为0")
                except ValueError:
                    thickness = 1  # 默认值
                    self.thickness_input.setText("1")
                    self.add_log_message(f"厚度参数无效，已重置为默认值: {thickness}")
                
                # 确定挤出方向
                direction_text = "向上" if thickness > 0 else "向下"
                self.add_log_message(f"使用挤出厚度: {thickness} ({direction_text})")
                
                # 计算挤出方向
                surface_mesh.compute_normals(cell_normals=True, point_normals=False, inplace=True)
                
                # 计算基础法向量
                if surface_mesh.n_cells > 0:
                    normals = surface_mesh.cell_normals
                    avg_normal = np.mean(normals, axis=0)
                    if np.linalg.norm(avg_normal) > 0:
                        base_normal = avg_normal / np.linalg.norm(avg_normal)
                    else:
                        base_normal = np.array([0, 0, 1]) # 默认Z轴方向
                else:
                    base_normal = np.array([0, 0, 1]) # 默认Z轴方向
                
                # 根据厚度正负决定最终挤出方向
                # 正值：沿法向挤出（向上）
                # 负值：沿法向反方向挤出（向下）
                if thickness > 0:
                    extrusion_vector = base_normal
                    actual_thickness = thickness
                else:
                    extrusion_vector = -base_normal  # 反转方向
                    actual_thickness = abs(thickness)  # 使用绝对值作为实际厚度
                
                self.add_log_message(f"基础法向: {base_normal}, 挤出方向: {extrusion_vector}, 实际厚度: {actual_thickness:.2f}")
                
                # 创建更稳定的挤出实体
                # 方法1: 使用标准挤出
                try:
                    extruded_solid = surface_mesh.extrude(extrusion_vector * actual_thickness, capping=True)
                    self.add_log_message("使用标准挤出方法成功")
                except Exception as e:
                    self.add_log_message(f"标准挤出失败: {e}, 尝试备用方法")
                    # 方法2: 手动创建挤出实体
                    try:
                        # 创建顶面和底面
                        bottom_surface = surface_mesh.copy()
                        top_surface = surface_mesh.copy()
                        top_surface.points = top_surface.points + extrusion_vector * actual_thickness
                        
                        # 合并顶底面并手动创建侧面
                        extruded_solid = bottom_surface.merge(top_surface)
                        self.add_log_message("使用手动挤出方法成功")
                    except Exception as e2:
                        self.add_log_message(f"手动挤出也失败: {e2}, 使用简单平移")
                        # 方法3: 简单平移
                        extruded_solid = surface_mesh.copy()
                        extruded_solid.points = extruded_solid.points + extrusion_vector * (actual_thickness / 2.0)

                self.add_log_message(f"已将TS面挤出为实体，原始厚度: {thickness:.2f}, 实际厚度: {actual_thickness:.2f}, 方向: {direction_text}")

                # 检查挤出实体的有效性
                self.add_log_message(f"检查挤出实体: {extruded_solid.n_points}点/{extruded_solid.n_cells}面")
                
                if extruded_solid.n_points == 0 or extruded_solid.n_cells == 0:
                    raise ValueError("挤出实体为空，无法进行后续处理")
                
                # 直接使用PyMeshLab处理，避免tetgen的复杂性问题
                self.add_log_message("使用PyMeshLab处理挤出实体，确保水密性...")
                
                extruded_obj_path = os.path.join(self.output_folder, "surface_solid.obj")
                extruded_wrl_path = os.path.join(self.output_folder, "surface_solid.wrl")
                
                try:
                    # 先保存为OBJ格式
                    extruded_solid.save(extruded_obj_path)
                    self.add_log_message(f"已保存挤出实体为OBJ: {os.path.basename(extruded_obj_path)}")
                    
                    # 使用PyMeshLab处理和修复
                    ms = pymeshlab.MeshSet()
                    ms.load_new_mesh(extruded_obj_path)
                    
                    # 检查加载的网格
                    mesh = ms.current_mesh()
                    self.add_log_message(f"PyMeshLab加载网格: {mesh.vertex_number()}顶点, {mesh.face_number()}面")
                    
                    if mesh.vertex_number() == 0 or mesh.face_number() == 0:
                        raise ValueError("PyMeshLab加载的网格为空")
                    
                    # 基础清理
                    self.add_log_message("  - 步骤1: 基础网格清理...")
                    ms.meshing_remove_duplicate_vertices()
                    ms.meshing_remove_duplicate_faces()
                    ms.meshing_remove_unreferenced_vertices()
                    
                    # 修复非流形结构
                    self.add_log_message("  - 步骤2: 修复非流形结构...")
                    ms.meshing_repair_non_manifold_edges()
                    ms.meshing_repair_non_manifold_vertices()
                    
                    # 统一法向
                    self.add_log_message("  - 步骤3: 统一法向...")
                    ms.meshing_re_orient_faces_coherently()
                    ms.compute_normal_per_face()
                    ms.compute_normal_per_vertex()
                    
                    # 检查并封闭孔洞
                    self.add_log_message("  - 步骤4: 检查并封闭孔洞...")
                    try:
                        ms.meshing_close_holes(maxholesize=50)
                    except:
                        self.add_log_message("    警告: 孔洞封闭失败，继续处理...")
                    
                    # 最终检查
                    final_mesh = ms.current_mesh()
                    self.add_log_message(f"处理完成: {final_mesh.vertex_number()}顶点, {final_mesh.face_number()}面")
                    
                    if final_mesh.vertex_number() == 0 or final_mesh.face_number() == 0:
                        raise ValueError("处理后的网格为空")
                    
                    # 保存为OBJ（作为备用）
                    ms.save_current_mesh(extruded_obj_path)
                    
                    # 立即尝试保存为TS格式（从PyMeshLab网格）
                    try:
                        extruded_immediate_ts_path = os.path.join(self.output_folder, "surface_solid.ts")
                        # 从PyMeshLab获取顶点和面数据
                        vertices = final_mesh.vertex_matrix()
                        faces = final_mesh.face_matrix()
                        
                        # 保存为TS格式
                        if self.write_ts_file(extruded_immediate_ts_path, vertices, faces, name="surface_solid"):
                            self.add_log_message(f"已保存挤出实体为TS格式: {os.path.basename(extruded_immediate_ts_path)}")
                        else:
                            self.add_log_message("警告: 保存挤出实体TS格式失败")
                    except Exception as ts_error:
                        self.add_log_message(f"保存挤出实体TS格式时出错: {ts_error}")
                    
                    # 同时保存为WRL格式用于布尔运算
                    try:
                        # 先保存为PLY，再用PyVista转换为WRL
                        temp_ply_path = os.path.join(self.output_folder, "temp_surface_solid.ply")
                        ms.save_current_mesh(temp_ply_path)
                        
                        # 用PyVista读取并转换
                        pv_mesh = pv.read(temp_ply_path)
                        plotter = pv.Plotter(off_screen=True)
                        plotter.add_mesh(pv_mesh)
                        plotter.export_vrml(extruded_wrl_path)
                        
                        # 清理临时文件
                        if os.path.exists(temp_ply_path):
                            os.remove(temp_ply_path)
                            
                        self.add_log_message(f"已保存处理后的挤出实体为WRL: {os.path.basename(extruded_wrl_path)}")
                        
                    except Exception as wrl_error:
                        self.add_log_message(f"WRL转换失败: {wrl_error}")
                        # 如果WRL转换失败，直接使用OBJ文件
                        extruded_wrl_path = extruded_obj_path
                        self.add_log_message("将使用OBJ格式进行布尔运算")
                        
                except Exception as e:
                    self.add_log_message(f"PyMeshLab处理失败: {e}")
                    import traceback
                    self.add_log_message(traceback.format_exc())
                    
                    # 最后的备用方案：直接使用原始挤出实体
                    self.add_log_message("使用原始挤出实体作为最后备用方案...")
                    try:
                        extruded_solid.save(extruded_obj_path)
                        extruded_wrl_path = extruded_obj_path
                                                self.add_log_message("已保存原始挤出实体为OBJ格式")
                    except Exception as final_error:
                        raise ValueError(f"所有保存方法都失败: {final_error}")

                # 【新增】挤出实体创建完成后进行坐标标准化检查
                self.add_log_message("=== 挤出实体完成，开始坐标标准化检查 ===")
                
                # 检查挤出实体坐标范围
                try:
                    if os.path.exists(extruded_obj_path):
                        extruded_mesh_pv = pv.read(extruded_obj_path)
                        extruded_bounds = extruded_mesh_pv.bounds
                        max_extruded_coord = max(abs(extruded_bounds[0]), abs(extruded_bounds[1]), abs(extruded_bounds[2]), abs(extruded_bounds[3]))
                        self.add_log_message(f"挤出实体坐标范围检查，最大坐标值: {max_extruded_coord:.0f}")
                    else:
                        self.add_log_message("挤出实体OBJ文件不存在，使用模型坐标作为参考")
                        max_extruded_coord = max_model_coord
                except Exception as e:
                    self.add_log_message(f"读取挤出实体坐标失败: {e}，使用模型坐标作为参考")
                    max_extruded_coord = max_model_coord
                
                # 决定是否需要坐标标准化
                max_overall_coord = max(max_model_coord, max_extruded_coord)
                
                if max_overall_coord > 1000000:  # 如果坐标值大于100万，进行标准化
                    self.add_log_message(f"检测到大坐标值 (最大: {max_overall_coord:.0f})，执行坐标标准化...")
                    
                    try:
                        # 创建临时模型OBJ文件
                        temp_model_obj = os.path.join(self.output_folder, "temp_model_for_norm.obj")
                        if not self.write_obj_file(temp_model_obj, model_vertices, model_faces):
                            raise ValueError("无法创建模型临时OBJ文件")
                        
                        # 确保挤出实体OBJ文件存在
                        if not os.path.exists(extruded_obj_path):
                            raise ValueError(f"挤出实体OBJ文件不存在: {extruded_obj_path}")
                        
                        # 读取OBJ文件进行坐标标准化
                        model_mesh_for_norm = pv.read(temp_model_obj)
                        extruded_mesh_for_norm = pv.read(extruded_obj_path)
                        
                        # 进行坐标标准化
                        model_norm, extruded_norm, coord_offset = normalize_mesh_coordinates(model_mesh_for_norm, extruded_mesh_for_norm, verbose=True)
                        
                        # 保存标准化后的网格
                        model_norm_path = os.path.join(self.output_folder, "model_for_surface_op_normalized.obj")
                        extruded_norm_path = os.path.join(self.output_folder, "surface_solid_normalized.obj")
                        
                        model_norm.save(model_norm_path)
                        extruded_norm.save(extruded_norm_path)
                        
                        self.add_log_message(f"已保存标准化网格: {os.path.basename(model_norm_path)}, {os.path.basename(extruded_norm_path)}")
                        self.add_log_message(f"坐标偏移量: X={coord_offset[0]:.2f}, Y={coord_offset[1]:.2f}, Z={coord_offset[2]:.2f}")
                        
                        # 使用标准化后的文件进行布尔运算
                        model_for_boolean = model_norm_path
                        extruded_for_boolean = extruded_norm_path
                        coordinate_normalized = True
                        
                        # 清理临时文件
                        if os.path.exists(temp_model_obj):
                            os.remove(temp_model_obj)
                            
                    except Exception as norm_error:
                        self.add_log_message(f"坐标标准化失败: {norm_error}，使用原始文件")
                        model_for_boolean = model_wrl_path
                        extruded_for_boolean = extruded_wrl_path
                        coordinate_normalized = False
                        coord_offset = np.array([0, 0, 0])
                        
                else:
                    self.add_log_message(f"坐标值合理 (最大: {max_overall_coord:.0f})，无需标准化")
                    model_for_boolean = model_wrl_path
                    extruded_for_boolean = extruded_wrl_path
                    coordinate_normalized = False
                    coord_offset = np.array([0, 0, 0])

                # 3. 执行布尔运算
                self.statusBar.showMessage("正在执行布尔运算...")

                # 输出文件路径
                intersection_obj = os.path.join(self.output_folder, "surface_op_intersection.obj")
                intersection_ts = os.path.join(self.output_folder, "surface_op_intersection.ts")
                
                difference_obj = os.path.join(self.output_folder, "surface_op_difference.obj")
                difference_ts = os.path.join(self.output_folder, "surface_op_difference.ts")

                union_obj = os.path.join(self.output_folder, "surface_op_union.obj")
                union_ts = os.path.join(self.output_folder, "surface_op_union.ts")
                
                # 验证输入文件
                self.add_log_message("验证布尔运算输入文件...")
                
                if not os.path.exists(model_for_boolean):
                    raise ValueError(f"模型文件不存在: {model_for_boolean}")
                if not os.path.exists(extruded_for_boolean):
                    raise ValueError(f"挤出实体文件不存在: {extruded_for_boolean}")
                
                # 执行布尔运算前的网格验证
                def validate_mesh_for_boolean(file_path, name):
                    try:
                        test_ms = pymeshlab.MeshSet()
                        test_ms.load_new_mesh(file_path)
                        mesh = test_ms.current_mesh()
                        
                        if mesh.vertex_number() == 0:
                            raise ValueError(f"{name} 顶点数为0")
                        if mesh.face_number() == 0:
                            raise ValueError(f"{name} 面数为0")
                        
                        self.add_log_message(f"{name} 验证通过: {mesh.vertex_number()}顶点, {mesh.face_number()}面")
                        return True
                    except Exception as e:
                        self.add_log_message(f"{name} 验证失败: {e}")
                        return False
                
                # 验证两个输入网格
                model_valid = validate_mesh_for_boolean(model_for_boolean, "TS模型")
                surface_valid = validate_mesh_for_boolean(extruded_for_boolean, "挤出实体")
                
                if not model_valid or not surface_valid:
                    raise ValueError("输入网格验证失败，无法执行布尔运算")

                # 执行布尔运算并增强错误处理
                boolean_operations = [
                    ("差集运算 (模型 - 挤出体)", "difference", difference_obj, difference_ts, "surface_op_difference"),
                    ("交集运算 (模型 & 挤出体)", "intersection", intersection_obj, intersection_ts, "surface_op_intersection"),
                    ("并集运算 (模型 + 挤出体)", "union", union_obj, union_ts, "surface_op_union")
                ]
                
                successful_operations = 0
                
                for op_name, op_type, obj_file, ts_file, ts_name in boolean_operations:
                    try:
                        self.add_log_message(f"开始{op_name}...")
                        
                        ms_op = pymeshlab.MeshSet()
                        ms_op.load_new_mesh(model_for_boolean)
                        ms_op.load_new_mesh(extruded_for_boolean)
                        
                        # 执行对应的布尔运算
                        if op_type == "difference":
                            ms_op.generate_boolean_difference(first_mesh=0, second_mesh=1)
                        elif op_type == "intersection":
                            ms_op.generate_boolean_intersection(first_mesh=0, second_mesh=1)
                        elif op_type == "union":
                            ms_op.generate_boolean_union(first_mesh=0, second_mesh=1)
                        
                        # 删除原始输入网格，只保留结果
                        ms_op.set_current_mesh(1)
                        ms_op.delete_current_mesh()
                        
                        # 验证结果
                        result_mesh = ms_op.current_mesh()
                        if result_mesh.vertex_number() == 0 or result_mesh.face_number() == 0:
                            self.add_log_message(f"  警告: {op_name}结果为空")
                            continue
                        
                        self.add_log_message(f"  {op_name}成功: {result_mesh.vertex_number()}顶点, {result_mesh.face_number()}面")
                        
                        # 保存结果 - 使用新的坐标还原辅助函数
                        if save_boolean_result_with_coordinate_restoration(self, ms_op, obj_file, ts_file, op_name, coordinate_normalized, coord_offset):
                            self.add_log_message(f"  已保存{op_name}结果: {os.path.basename(obj_file)} 和 {os.path.basename(ts_file)}")
                            successful_operations += 1
                        else:
                            self.add_log_message(f"  {op_name}结果保存失败")
                            
                    except Exception as op_error:
                        self.add_log_message(f"  {op_name}执行失败: {op_error}")
                        # 继续执行其他布尔运算
                        continue
                
                # 4. 导出挤出体为TS格式 (增强版本)
                self.add_log_message("4. 导出挤出体为TS格式 (增强版本)")
                try:
                    extruded_ts_path = os.path.join(self.output_folder, "extruded_surface.ts")
                    extruded_direct_ts_path = os.path.join(self.output_folder, "extruded_surface_direct.ts")
                    
                    # 方法1: 从挤出实体的OBJ文件转换为TS格式
                    method1_success = False
                    if os.path.exists(extruded_obj_path):
                        self.add_log_message(f"  方法1: 从OBJ文件转换 ({os.path.basename(extruded_obj_path)})")
                        if self.obj_to_ts(extruded_obj_path, extruded_ts_path, name="extruded_surface"):
                            self.add_log_message(f"  ✅ 方法1成功: {os.path.basename(extruded_ts_path)}")
                            successful_operations += 1
                            method1_success = True
                        else:
                            self.add_log_message(f"  ❌ 方法1失败: OBJ到TS转换失败")
                    else:
                        self.add_log_message(f"  ❌ 方法1失败: OBJ文件不存在 ({extruded_obj_path})")
                    
                    # 方法2: 直接从PyVista网格导出TS格式 (备用方案)
                    if not method1_success and 'extruded_solid' in locals():
                        self.add_log_message(f"  方法2: 直接从PyVista网格导出")
                        try:
                            # 从PyVista网格直接提取顶点和面
                            vertices = np.array(extruded_solid.points)
                            faces = []
                            
                            # 提取三角形面
                            i = 0
                            while i < len(extruded_solid.faces):
                                n_points = extruded_solid.faces[i]
                                if n_points == 3:
                                    faces.append([
                                        extruded_solid.faces[i + 1],
                                        extruded_solid.faces[i + 2],
                                        extruded_solid.faces[i + 3]
                                    ])
                                i += n_points + 1
                            
                            faces = np.array(faces)
                            
                            if len(vertices) > 0 and len(faces) > 0:
                                if self.write_ts_file(extruded_direct_ts_path, vertices, faces, name="extruded_surface_direct"):
                                    self.add_log_message(f"  ✅ 方法2成功: {os.path.basename(extruded_direct_ts_path)}")
                                    successful_operations += 1
                                    method1_success = True
                                else:
                                    self.add_log_message(f"  ❌ 方法2失败: 直接TS导出失败")
                            else:
                                self.add_log_message(f"  ❌ 方法2失败: 无效的顶点或面数据 (顶点:{len(vertices)}, 面:{len(faces)})")
                        except Exception as direct_error:
                            self.add_log_message(f"  ❌ 方法2异常: {str(direct_error)}")
                    
                    # 方法3: 从PyMeshLab处理后的网格导出 (最终备用方案)
                    if not method1_success:
                        self.add_log_message(f"  方法3: 尝试从PyMeshLab处理后的数据导出")
                        try:
                            # 检查是否有PyMeshLab处理过的数据
                            fallback_ts_path = os.path.join(self.output_folder, "extruded_surface_fallback.ts") 
                            # 这里可以添加从其他来源获取数据的逻辑
                            # 暂时标记为未成功，但记录尝试
                            self.add_log_message(f"  方法3: 当前没有可用的PyMeshLab数据")
                        except Exception as fallback_error:
                            self.add_log_message(f"  ❌ 方法3异常: {str(fallback_error)}")
                    
                    # 总结挤出体导出结果
                    if method1_success:
                        self.add_log_message(f"✅ 挤出体TS导出成功完成")
                    else:
                        self.add_log_message(f"⚠️ 挤出体TS导出失败，请检查以上错误信息")
                        
                except Exception as extrude_error:
                    self.add_log_message(f"❌ 导出挤出体TS格式时发生严重错误: {extrude_error}")
                    import traceback
                    self.add_log_message(f"详细错误信息: {traceback.format_exc()}")

                # 根据成功的运算数量给出反馈
                total_possible_outputs = len(boolean_operations) + 1  # 布尔运算结果 + 挤出体
                if successful_operations == 0:
                    raise ValueError("所有布尔运算都失败了")
                elif successful_operations < total_possible_outputs:
                    self.add_log_message(f"警告: 只有 {successful_operations}/{total_possible_outputs} 个运算成功")
                else:
                    self.add_log_message("所有运算都成功完成")

                # 显示最终结果
                if successful_operations == total_possible_outputs:
                    success_msg = f"TS模型与TS面运算完成！\n成功生成了所有 {successful_operations} 种结果：\n• 差集 (模型-面)\n• 交集 (模型∩面)\n• 并集 (模型∪面)\n• 挤出体 TS格式"
                    self.statusBar.showMessage("TS模型与TS面运算完全成功")
                else:
                    success_msg = f"TS模型与TS面运算部分完成\n成功生成了 {successful_operations}/{total_possible_outputs} 种结果\n请查看日志了解详细信息"
                    self.statusBar.showMessage(f"TS模型与TS面运算部分成功 ({successful_operations}/{total_possible_outputs})")
                
                QMessageBox.information(self, "运算完成", success_msg)

            except Exception as e:
                self.add_log_message(f"执行TS模型与TS面运算时发生错误: {str(e)}")
                import traceback
                self.add_log_message(traceback.format_exc())
                
                # 程序不会因错误而退出，只显示错误信息
                try:
                    QMessageBox.critical(self, "错误", f"处理时发生错误:\n{str(e)}\n\n程序将继续运行，您可以查看日志了解详细信息。")
                except:
                    # 如果连消息框都显示不了，至少在日志中记录
                    self.add_log_message("无法显示错误对话框，但程序继续运行")
                
                self.statusBar.showMessage("运算失败，但程序继续运行")
                self.add_log_message("⚠️ 虽然发生错误，但程序将继续运行以便您查看日志和检查文件")
                return False
        except Exception as outer_error:
            # 最外层的异常捕获，确保程序不会闪退
            self.add_log_message(f"执行过程中发生意外错误: {str(outer_error)}")
            import traceback
            self.add_log_message(traceback.format_exc())
            
            try:
                QMessageBox.critical(self, "严重错误", f"程序执行过程中发生意外错误:\n{str(outer_error)}\n\n程序将继续运行，但此次操作已中止。")
            except:
                self.add_log_message("无法显示错误对话框")
            
            self.statusBar.showMessage("操作失败，但程序继续运行")
            return False

# 添加统一法向量的函数
def unify_normals(mesh, inside_point=None, outward=True):
    """
    统一网格的法向量方向，确保整个网格面片法向一致 (PyVista)
    
    参数:
    mesh: PyVista网格对象
    inside_point: 内部参考点
    outward: 如果为True，则法向朝外；如果为False，则法向朝内
    
    返回:
    统一法向后的网格
    """
    print(f"开始统一网格法向 (PyVista)，朝向：{'外部' if outward else '内部'}")
    
    if mesh.n_points == 0 or mesh.n_cells == 0:
        print("警告: 输入的网格为空，无法统一法向 (PyVista)")
        return mesh.copy() # 返回副本
    
    result_mesh = mesh.copy() # 操作副本
    
    try:
        # 确保是三角形网格
        # PyVista的PolyData.triangulate()是filter, 返回新网格, 或用inplace=True修改
        result_mesh = result_mesh.triangulate() # 确保返回的是新对象或修改了副本
        print("已将网格转换为三角形网格 (PyVista)")
    except Exception as e:
        print(f"警告: 三角化失败 (PyVista): {e}")

    try:
        # 步骤1: 计算法线并尝试使其一致朝外/内
        # cell_normals=True, point_normals=False (通常处理面法向)
        # consistent_normals=True 尝试使法线方向一致
        # auto_orient_normals=True 尝试使法线朝外（如果outward=True）
        # non_manifold_traversal=True 用于更复杂的网格
        
        # 确定法线是否应该自动朝外
        # 如果 outward is True, we want auto_orient_normals to try and point them out.
        # If outward is False, we first orient them outward, then flip.
        result_mesh.compute_normals(cell_normals=True, point_normals=False,
                                    consistent_normals=True,
                                    auto_orient_normals=True, # Try to orient outward first
                                    non_manifold_traversal=True, # More robust for complex meshes
                                    inplace=True)
        print("PyVista: compute_normals(consistent_normals=True, auto_orient_normals=True, non_manifold_traversal=True) 应用完成.")

        # 步骤2: 如果要求朝内，则翻转
        if not outward:
            # This flip assumes compute_normals with auto_orient_normals=True made them point outward
            result_mesh.flip_normals(inplace=True) # 反转所有面法线
            print("PyVista: 法向已翻转为朝内.")
            
        print("成功统一网格法向 (PyVista)")
        return result_mesh
    except Exception as e:
        # 使用traceback打印更详细的错误信息
        print(f"警告: 使用PyVista统一法向失败: {e}. 详细信息: {traceback.format_exc()}")
        # 失败时返回原始网格的副本，以避免后续操作在None上失败
        return mesh.copy()


def unify_boundary_normals(mesh):
    """
    为特定结构的边界体（顶底+侧面）统一法向量
    这个方法专门针对采空区边界体的几何特征：水平的顶底面和垂直的侧面
    
    参数:
    mesh: PyVista网格对象
    
    返回:
    统一法向后的网格
    """
    print("开始统一边界体法向，使用几何特征法...")
    
    # 确保输入的网格有效
    if mesh.n_points == 0 or mesh.n_cells == 0:
        print("警告: 输入的网格为空，无法统一法向")
        return mesh
    
    # 创建网格的副本
    result_mesh = mesh.copy()
    
    # 确保是三角形网格
    try:
        if not mesh.is_all_triangles():
            result_mesh = result_mesh.triangulate()
    except:
        try:
            # 备选三角化方法
            result_mesh = result_mesh.triangulate()
        except Exception as e:
            print(f"三角化失败，继续使用原始网格: {e}")
    
    # 计算每个面的法向量
    try:
        # 生成新的网格
        print("使用PyVista的fix_normals方法修复法向...")
        fixed_mesh = result_mesh.copy()
        fixed_mesh.compute_normals(inplace=True)
        fixed_mesh.point_normals_to_cell_normals(inplace=True)
        
        # 应用fix_normals
        fixed_mesh_fixed = fixed_mesh.fix_normals(inplace=False)
        print("法向修复完成")
        return fixed_mesh_fixed
    except Exception as e:
        print(f"使用fix_normals失败，尝试备选方法: {e}")
        
        try:
            # 备选方法：直接翻转所有面的法向，保证统一
            print("使用flip_normals作为备选方法...")
            flipped = result_mesh.copy()
            flipped = flipped.compute_normals(inplace=False)
            
            # 检查是否需要翻转
            # 自定义检查：边界体的底部应该朝下，顶部朝上
            center = result_mesh.center
            bottom_faces = []
            top_faces = []
            side_faces = []
            
            # 识别顶部、底部和侧面
            for i in range(result_mesh.n_cells):
                cell = result_mesh.extract_cells(i)
                normal = cell.cell_normals[0]  # 只有一个面
                
                # 根据法向量的Z值区分不同类型的面
                if abs(normal[2]) > 0.7:  # Z分量占主导，是水平面
                    if normal[2] < 0:  # 朝下
                        bottom_faces.append(i)
                    else:  # 朝上
                        top_faces.append(i)
                else:  # 侧面
                    side_faces.append(i)
            
            print(f"识别出 {len(top_faces)} 个顶面, {len(bottom_faces)} 个底面, {len(side_faces)} 个侧面")
            
            if len(top_faces) + len(bottom_faces) + len(side_faces) == 0:
                print("无法识别面的类型，返回原始网格")
                return result_mesh
                
            # 创建固定了法向的新网格
            if top_faces or bottom_faces or side_faces:
                all_meshes = []
                
                # 处理顶面 - 应该朝上
                if top_faces:
                    top_mesh = result_mesh.extract_cells(top_faces)
                    # 检查是否需要翻转
                    avg_normal = np.mean(top_mesh.cell_normals, axis=0)
                    if avg_normal[2] < 0:  # 如果平均法向朝下，翻转
                        top_mesh = top_mesh.flip_normals()
                    all_meshes.append(top_mesh)
                
                # 处理底面 - 现在也应该朝上
                if bottom_faces:
                    bottom_mesh = result_mesh.extract_cells(bottom_faces)
                    # 检查是否需要翻转
                    avg_normal = np.mean(bottom_mesh.cell_normals, axis=0)
                    if avg_normal[2] < 0:  # 如果平均法向朝下，翻转
                        bottom_mesh = bottom_mesh.flip_normals()
                    all_meshes.append(bottom_mesh)
                
                # 处理侧面 - 法向应该朝外（远离中心点）
                if side_faces:
                    side_mesh = result_mesh.extract_cells(side_faces)
                    # 计算每个面的中心点
                    cell_centers = side_mesh.cell_centers()
                    
                    # 检查每个侧面的朝向
                    need_flip = []
                    for i in range(side_mesh.n_cells):
                        normal = side_mesh.cell_normals[i]
                        # 将法向量Z分量归零，只考虑XY平面方向
                        normal_xy = normal.copy()
                        normal_xy[2] = 0
                        normal_xy_norm = np.linalg.norm(normal_xy)
                        if normal_xy_norm > 0:
                            normal_xy = normal_xy / normal_xy_norm
                            
                            # 获取从中心指向面中心的方向
                            cell_center = cell_centers.points[i]
                            center_to_face = cell_center[:2] - center[:2]  # 只考虑XY平面
                            center_to_face_norm = np.linalg.norm(center_to_face)
                            if center_to_face_norm > 0:
                                center_to_face = center_to_face / center_to_face_norm
                                
                                # 计算点积，判断方向是否一致
                                dot_product = np.dot(normal_xy, center_to_face)
                                if dot_product < 0:  # 法向朝内，需要翻转
                                    need_flip.append(i)
                    
                    if need_flip:
                        print(f"需要翻转 {len(need_flip)} 个侧面")
                        flip_side_mesh = side_mesh.extract_cells(need_flip)
                        flip_side_mesh = flip_side_mesh.flip_normals()
                        
                        # 保留不需要翻转的面
                        keep_indices = [i for i in range(side_mesh.n_cells) if i not in need_flip]
                        if keep_indices:
                            keep_side_mesh = side_mesh.extract_cells(keep_indices)
                            # 合并翻转和未翻转的部分
                            side_mesh = keep_side_mesh.merge(flip_side_mesh)
                        else:
                            side_mesh = flip_side_mesh
                    
                    all_meshes.append(side_mesh)
                
                # 合并所有修复后的部分
                if all_meshes:
                    final_mesh = all_meshes[0]
                    for mesh_part in all_meshes[1:]:
                        final_mesh = final_mesh.merge(mesh_part)
                    
                    print("基于几何特征的法向修复完成")
                    return final_mesh
            
            # 如果基于几何的方法失败，使用全局翻转作为最后手段
            return flipped
        
        except Exception as e:
            print(f"所有法向修复方法都失败: {e}")
            return result_mesh


# 添加专门处理boundary_original.obj的法向统一函数
def fix_boundary_obj_normals(obj_file, is_single_component=False):
    """
    专门修复边界体OBJ文件的法向问题，确保所有面的法向一致
    
    参数:
    obj_file: OBJ文件路径
    is_single_component: 是否为单个组件（用于不同的处理策略）
    
    返回:
    是否成功修复
    """
    print(f"开始修复 {obj_file} 的法向... (单个组件: {is_single_component})")
    try:
        import pymeshlab
        
        ms = pymeshlab.MeshSet()
        ms.load_new_mesh(obj_file)
        
        mesh = ms.current_mesh()
        if mesh.face_number() == 0:
            print(f"  警告: {obj_file} 不包含任何面，跳过修复。")
            return False # 不能算成功修复

        print(f"  加载网格: {mesh.vertex_number()}顶点, {mesh.face_number()}面")
        
        if is_single_component:
            print(f"  使用 detect_and_fix_normals 处理单组件 {obj_file}")
            detect_and_fix_normals(ms) # detect_and_fix_normals 内部有自己的修复逻辑
        else: # is_single_component=False (例如处理合并后的 boundary_original.obj)
            print(f"  使用多组件策略处理 {obj_file}")
            # 步骤1: 彻底清理网格
            print("    步骤1: 基本网格修复 (多组件)...")
            try:
                ms.meshing_remove_duplicate_vertices()
                ms.meshing_remove_duplicate_faces()
                ms.meshing_remove_degenerate_faces() # Changed from meshing_remove_zero_area_faces
                ms.meshing_repair_non_manifold_edges(method='Split Edges')
                ms.meshing_repair_non_manifold_vertices()
                print("    基础修复完成 (多组件)")
            except Exception as e:
                print(f"    基础修复出错(可忽略) (多组件): {e}")
            
            # 步骤2: 计算初始面法线
            print("    步骤2: 计算初始面法线 (多组件)...")
            try:
                ms.compute_normal_per_face()
                mesh = ms.current_mesh() # 更新 mesh 引用
                print("    初始面法向计算完成 (多组件)。")
            except Exception as e_init_normals_multi:
                print(f"    计算初始面法线出错(可忽略) (多组件): {e_init_normals_multi}")

            # 步骤3: 确立全局"上下"朝向
            print("    步骤3: 确立全局上下朝向 (多组件)...")
            try:
                if mesh.face_number() > 0: # 确保有面可供检查
                     # check_if_needs_flip内部会检查并计算法线如果不存在
                    if check_if_needs_flip(ms):
                        print(f"      检测到 {obj_file} (多组件) 可能全局翻转，执行翻转...")
                        ms.meshing_invert_face_orientation()
                        # 关键：翻转后必须重新计算法线
                        ms.compute_normal_per_face() 
                        print(f"      {obj_file} (多组件) 已全局翻转并重新计算面法线。")
                    else:
                        print(f"      {obj_file} (多组件) 检测结果为不需要全局翻转。")
                else:
                    print(f"      {obj_file} (多组件) 没有面，跳过翻转检查。")
            except Exception as e_flip_check_multi:
                print(f"    全局上下朝向检查/翻转时出错 (多组件): {e_flip_check_multi}")

            # 步骤4: 强制一致化朝向 (使侧面"朝外")
            print("    步骤4: 强制一致化朝向 (多组件)...")
            try:
                # 运行两次 meshing_re_orient_faces_coherently 来增强效果
                for i in range(2):
                    print(f"      多组件一致化尝试 {i+1}/2")
                    ms.meshing_re_orient_faces_coherently()
                print("    一致化朝向处理完成 (多组件)。")
            except Exception as e_orient_multi:
                print(f"    meshing_re_orient_faces_coherently() 出错 (多组件): {e_orient_multi}")

            # 步骤5: 最终重新计算所有法线
            print("    步骤5: 最终法向计算 (多组件)...")
            try:
                ms.compute_normal_per_vertex()
                ms.compute_normal_per_face()
            except Exception as e_final_normals_multi:
                print(f"    最终法向计算出错(可忽略) (多组件): {e_final_normals_multi}")
            print(f"  多组件策略处理 {obj_file} 完成。")
        
        # 保存修复后的网格
        fixed_file = obj_file.replace(".obj", "_fixed.obj")
        ms.save_current_mesh(fixed_file)
        
        # 如果成功，则将修复后的文件替换原始文件
        import os
        import shutil
        if os.path.exists(fixed_file):
            shutil.move(fixed_file, obj_file)
            print(f"已成功修复 {obj_file} 的法向，并替换原始文件")
            return True
        else:
            print(f"警告: 修复后的文件未生成")
            return False
    
    except Exception as e:
        print(f"修复法向时出错: {str(e)}")
        import traceback
        print(traceback.format_exc())
        return False


# 新增智能法向检测和修复函数
def detect_and_fix_normals(meshset):
    """
    智能检测和修复网格法向问题 (主要用于单个组件)
    
    参数:
    meshset: pymeshlab.MeshSet对象
    """
    print("  执行 detect_and_fix_normals (单个组件)...")
    current_mesh = meshset.current_mesh()
    if current_mesh.face_number() == 0:
        print("    警告: 网格没有面，跳过法向修复。")
        return

    # 步骤1: 修复基本网格问题
    print("    步骤1: 基本网格修复...")
    try:
        meshset.meshing_remove_duplicate_vertices()
        meshset.meshing_remove_duplicate_faces()
        meshset.meshing_remove_degenerate_faces() # Changed from meshing_remove_zero_area_faces
        meshset.meshing_repair_non_manifold_edges(method='Split Edges')
        meshset.meshing_repair_non_manifold_vertices()
    except Exception as e:
        print(f"    基本网格修复出错(可忽略): {e}")
    
    # 步骤2: 计算初始面法线
    print("    步骤2: 计算初始面法线...")
    try:
        meshset.compute_normal_per_face()
        # 更新current_mesh实例，以便后续has_face_normals()能获取到最新的状态
        current_mesh = meshset.current_mesh() 
    except Exception as e:
        print(f"    计算初始面法线出错(可忽略): {e}")
        # 如果这里失败，后续的check_if_needs_flip可能不准确
        # 但check_if_needs_flip内部也会尝试计算，所以流程可以继续

    # 步骤3: 确立全局"上下"朝向
    print("    步骤3: 确立全局上下朝向...")
    try:
        if current_mesh.face_number() > 0: # 再次检查面数量
            # check_if_needs_flip内部会检查并计算法线如果不存在
            if check_if_needs_flip(meshset):
                print("      检测到单个组件可能全局翻转，执行翻转...")
                meshset.meshing_invert_face_orientation()
                # 关键：翻转后必须重新计算法线，因为它们的方向变了
                meshset.compute_normal_per_face() 
                print("      单个组件已全局翻转并重新计算面法线。")
            else:
                print("      单个组件检测结果为不需要全局翻转。")
        else:
             print("      单个组件没有面，跳过翻转检查。")
    except Exception as e_flip_check_single:
        print(f"    单个组件全局上下朝向检查/翻转时出错: {e_flip_check_single}")
        
    # 步骤4: 强制一致化朝向 (使侧面"朝外")
    print("    步骤4: 强制一致化朝向...")
    try:
        # 迭代几次以增强一致性
        for i in range(2): # 运行两次
            print(f"      单个组件一致化尝试 {i+1}/2")
            meshset.meshing_re_orient_faces_coherently()
    except Exception as e_orient_single:
        print(f"    单个组件一致化朝向 (re_orient_faces_coherently) 出错: {e_orient_single}")
    
    # 步骤5: 最终重新计算所有法线
    print("    步骤5: 最终法向计算...")
    try:
        meshset.compute_normal_per_vertex()
        meshset.compute_normal_per_face()
    except Exception as e_final_normals_single:
        print(f"    单个组件最终法向计算出错(可忽略): {e_final_normals_single}")
    print("  detect_and_fix_normals (单个组件) 完成。")


def check_if_needs_flip(meshset):
    """
    检查网格是否需要整体翻转, 通过分析顶面和底面候选区域的法向

    参数:
    meshset: pymeshlab.MeshSet对象

    返回:
    bool: 如果需要翻转返回True
    """
    print("执行 check_if_needs_flip...")
    try:
        mesh = meshset.current_mesh()
        if mesh.face_number() == 0:
            print("  网格没有面，无法判断是否翻转。")
            return False

        # 确保面法线已计算
        face_normals_matrix = mesh.face_normal_matrix()
        # Check if face normals exist by trying to access them or checking their count
        # Also check if the number of normals matches the number of faces
        if (face_normals_matrix is None or
            face_normals_matrix.shape[0] == 0 or
            (mesh.face_number() > 0 and face_normals_matrix.shape[0] != mesh.face_number())):
            try:
                print("  面法线不存在、不完整或与面数不匹配，尝试计算...")
                meshset.compute_normal_per_face()
                mesh = meshset.current_mesh() # Re-fetch mesh object
                face_normals_matrix = mesh.face_normal_matrix() # Re-fetch normals
                if (face_normals_matrix is None or
                    face_normals_matrix.shape[0] == 0 or
                    (mesh.face_number() > 0 and face_normals_matrix.shape[0] != mesh.face_number())):
                    print("  计算面法线后仍然无法获取或不匹配，无法判断。")
                    return False # Cannot determine
                print("  已计算面法线。")
            except Exception as e_norm:
                print(f"  计算面法线失败: {e_norm}")
                return False # Cannot determine

        vertices = mesh.vertex_matrix()
        faces = mesh.face_matrix()
        face_normals = mesh.face_normal_matrix()

        if len(vertices) == 0 or len(faces) == 0 or len(face_normals) == 0:
            print("  顶点、面或面法线数据不足，无法判断。")
            return False

        # 计算面重心
        face_barycenters = np.array([vertices[face].mean(axis=0) for face in faces])

        # 计算模型边界框和Z轴相关阈值
        min_coord = vertices.min(axis=0)
        max_coord = vertices.max(axis=0)
        min_z, max_z = min_coord[2], max_coord[2]
        
        if max_z == min_z: # 处理扁平模型的情况
            print("  模型在Z轴上是扁平的，跳过基于顶/底的翻转检查。")
            return False

        # 定义顶部和底部区域的Z阈值 (例如，模型高度的后1/3和前1/3)
        z_extent = max_z - min_z
        upper_z_threshold = max_z - z_extent * 0.33
        lower_z_threshold = min_z + z_extent * 0.33
        
        print(f"  Z范围: [{min_z:.2f}, {max_z:.2f}], 上部Z阈值: >{upper_z_threshold:.2f}, 下部Z阈值: <{lower_z_threshold:.2f}")

        top_candidates_normals_z = []
        bottom_candidates_normals_z = []

        # 筛选候选的顶面和底面
        for i in range(len(faces)):
            barycenter_z = face_barycenters[i, 2]
            normal_z = face_normals[i, 2]

            # 只考虑接近水平的面 (法线Z分量绝对值较大)
            if abs(normal_z) > 0.7: 
                if barycenter_z >= upper_z_threshold:
                    top_candidates_normals_z.append(normal_z)
                elif barycenter_z <= lower_z_threshold:
                    bottom_candidates_normals_z.append(normal_z)
        
        print(f"  找到 {len(top_candidates_normals_z)} 个顶部候选面, {len(bottom_candidates_normals_z)} 个底部候选面。")

        if not top_candidates_normals_z and not bottom_candidates_normals_z:
            print("  未找到足够的顶部或底部候选面进行判断。")
            # 可以考虑一个备用策略，比如之前的简单Z法向统计
            # total_negative_z_normals = np.sum(face_normals[:, 2] < -0.1)
            # total_positive_z_normals = np.sum(face_normals[:, 2] > 0.1)
            # if total_negative_z_normals > total_positive_z_normals * 1.2: # 多数朝下
            #    print("  备用策略：多数法线Z分量为负，建议翻转。")
            #    return True
            return False


        # 分析顶部候选面
        top_pointing_up = sum(1 for nz in top_candidates_normals_z if nz > 0.5)
        top_pointing_down = sum(1 for nz in top_candidates_normals_z if nz < -0.5)
        
        # 分析底部候选面
        bottom_pointing_up = sum(1 for nz in bottom_candidates_normals_z if nz > 0.5)
        bottom_pointing_down = sum(1 for nz in bottom_candidates_normals_z if nz < -0.5)

        print(f"  顶部候选: {top_pointing_up} 朝上, {top_pointing_down} 朝下。")
        print(f"  底部候选: {bottom_pointing_up} 朝上, {bottom_pointing_down} 朝下。")

        needs_flip = False
        # 决策逻辑：
        # 主要判断：如果顶部面主要朝下，且/或底部面主要朝上，则可能需要翻转
        if len(top_candidates_normals_z) > 0 and len(bottom_candidates_normals_z) > 0:
            # 同时有顶有底的情况
            if top_pointing_down > top_pointing_up and bottom_pointing_up > bottom_pointing_down:
                print("  决策: 顶部主要朝下且底部主要朝上 -> 建议翻转。")
                needs_flip = True
            elif top_pointing_down > top_pointing_up * 1.5: # 如果顶部绝大多数朝下
                 print("  决策: 顶部绝大多数朝下 -> 建议翻转。")
                 needs_flip = True
            elif bottom_pointing_up > bottom_pointing_down * 1.5: # 如果底部绝大多数朝上
                 print("  决策: 底部绝大多数朝上 -> 建议翻转。")
                 needs_flip = True
        elif len(top_candidates_normals_z) > 0: # 只有顶部候选
            if top_pointing_down > top_pointing_up:
                print("  决策: 只有顶部候选，且主要朝下 -> 建议翻转。")
                needs_flip = True
        elif len(bottom_candidates_normals_z) > 0: # 只有底部候选
            if bottom_pointing_up > bottom_pointing_down:
                print("  决策: 只有底部候选，且主要朝上 -> 建议翻转。")
                needs_flip = True
        
        if needs_flip:
            print("  结论: check_if_needs_flip 返回 True (需要翻转)")
        else:
            print("  结论: check_if_needs_flip 返回 False (不需要翻转)")
            
        return needs_flip

    except Exception as e:
        print(f"  check_if_needs_flip 执行时出错: {str(e)}")
        import traceback
        print(traceback.format_exc())
        return False # 出错时默认不翻转

# 全局异常处理函数
def handle_exception(exc_type, exc_value, exc_traceback):
    """全局异常处理器，确保程序不会因为未捕获的异常而闪退"""
    import sys
    import traceback
    
    # 忽略键盘中断 (Ctrl+C)
    if issubclass(exc_type, KeyboardInterrupt):
        sys.__excepthook__(exc_type, exc_value, exc_traceback)
        return
    
    # 记录异常信息
    error_msg = ''.join(traceback.format_exception(exc_type, exc_value, exc_traceback))
    print(f"[全局异常处理] 捕获到未处理的异常:")
    print(error_msg)
    
    # 如果可能，尝试显示错误对话框
    try:
        from PyQt5.QtWidgets import QMessageBox, QApplication
        app = QApplication.instance()
        if app is not None:
            msg = QMessageBox()
            msg.setIcon(QMessageBox.Critical)
            msg.setWindowTitle("程序错误")
            msg.setText("程序遇到了一个错误，但会继续运行")
            msg.setDetailedText(f"错误类型: {exc_type.__name__}\n错误信息: {str(exc_value)}\n\n详细堆栈:\n{error_msg}")
            msg.setStandardButtons(QMessageBox.Ok)
            msg.exec_()
    except Exception as dialog_error:
        print(f"[全局异常处理] 无法显示错误对话框: {dialog_error}")

# 安全的模块导入函数
def safe_import():
    """安全导入所有必要的模块，失败时给出清晰的错误信息"""
    missing_modules = []
    
    # 检查关键模块
    required_modules = [
        ('sys', 'sys'),
        ('os', 'os'),
        ('numpy', 'numpy'),
        ('PyQt5.QtWidgets', 'PyQt5'),
        ('PyQt5.QtGui', 'PyQt5'),
        ('PyQt5.QtCore', 'PyQt5'),
        ('pyvista', 'pyvista'),
        ('open3d', 'open3d'),
        ('ezdxf', 'ezdxf')
    ]
    
    for module_name, display_name in required_modules:
        try:
            __import__(module_name)
        except ImportError:
            missing_modules.append(display_name)
    
    if missing_modules:
        missing_str = ', '.join(set(missing_modules))
        print(f"[错误] 缺少必要的模块: {missing_str}")
        print("请使用以下命令安装缺少的模块:")
        if 'PyQt5' in missing_modules:
            print("  pip install PyQt5")
        if 'numpy' in missing_modules:
            print("  pip install numpy")
        if 'pyvista' in missing_modules:
            print("  pip install pyvista")
        if 'open3d' in missing_modules:
            print("  pip install open3d")
        if 'ezdxf' in missing_modules:
            print("  pip install ezdxf")
        return False
    
    return True

# 主程序入口
if __name__ == "__main__":
    # 设置全局异常处理器
    import sys
    sys.excepthook = handle_exception
    
    try:
        print("[启动] 开始启动采空区处理集成工具...")
        
        # 安全导入模块
        if not safe_import():
            print("[启动失败] 缺少必要的模块，程序无法启动")
            input("按回车键退出...")
            sys.exit(1)
        
        print("[启动] 模块导入成功")
        
        # 创建应用程序实例
        try:
            app = QApplication(sys.argv)
            print("[启动] QApplication 创建成功")
        except Exception as app_error:
            print(f"[启动失败] 无法创建QApplication: {app_error}")
            input("按回车键退出...")
            sys.exit(1)
        
        # 创建主窗口
        try:
            print("[启动] 正在初始化主窗口...")
            processor = IntegratedProcessor()
            print("[启动] 主窗口初始化成功")
        except Exception as init_error:
            print(f"[启动失败] 主窗口初始化失败: {init_error}")
            import traceback
            print(traceback.format_exc())
            
            # 即使初始化失败，也尝试显示一个最基本的错误窗口
            try:
                from PyQt5.QtWidgets import QWidget, QVBoxLayout, QLabel, QPushButton, QTextEdit
                
                error_window = QWidget()
                error_window.setWindowTitle("启动错误")
                error_window.setGeometry(100, 100, 600, 400)
                
                layout = QVBoxLayout()
                
                error_label = QLabel("程序启动失败，但您可以查看详细错误信息：")
                layout.addWidget(error_label)
                
                error_text = QTextEdit()
                error_text.setPlainText(f"错误信息：{str(init_error)}\n\n详细堆栈：\n{traceback.format_exc()}")
                error_text.setReadOnly(True)
                layout.addWidget(error_text)
                
                close_button = QPushButton("关闭")
                close_button.clicked.connect(lambda: sys.exit(0))
                layout.addWidget(close_button)
                
                error_window.setLayout(layout)
                error_window.show()
                
                print("[启动] 显示错误窗口")
                app.exec_()
                
            except Exception as error_window_error:
                print(f"[启动失败] 连错误窗口都无法显示: {error_window_error}")
                input("按回车键退出...")
            
            sys.exit(1)
        
        # 显示主窗口
        try:
            processor.show()
            print("[启动] 主窗口显示成功")
            print("[启动] 程序启动完成！")
        except Exception as show_error:
            print(f"[启动失败] 无法显示主窗口: {show_error}")
            import traceback
            print(traceback.format_exc())
            input("按回车键退出...")
            sys.exit(1)
        
        # 运行应用程序主循环
        try:
            print("[运行] 进入主事件循环...")
            app.exec_()
            print("[退出] 程序正常退出")
        except Exception as exec_error:
            print(f"[运行错误] 主事件循环异常: {exec_error}")
            import traceback
            print(traceback.format_exc())
            print("[运行] 程序将继续运行...")
        
    except Exception as main_error:
        print(f"[严重错误] 主程序发生严重错误: {main_error}")
        import traceback
        print(traceback.format_exc())
        input("按回车键退出...")
        sys.exit(1)